{"name": "backend", "version": "1.0.0", "description": "Node.js Express TypeScript Backend API", "main": "dist/server.js", "scripts": {"dev": "nodemon src/server.ts", "build": "tsc", "start": "node dist/server.js", "start:dev": "ts-node src/server.ts", "clean": "rm -rf dist", "build:watch": "tsc --watch", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["nodejs", "express", "typescript", "api", "backend"], "author": "", "license": "ISC", "dependencies": {"cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.21.2", "fluent-ffmpeg": "^2.1.3", "helmet": "^8.1.0", "morgan": "^1.10.0"}, "devDependencies": {"@types/cors": "^2.8.19", "@types/express": "^4.17.23", "@types/fluent-ffmpeg": "^2.1.27", "@types/morgan": "^1.9.10", "@types/node": "^24.0.3", "nodemon": "^3.1.10", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}