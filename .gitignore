# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Distribution directory
dist/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# nyc test coverage
.nyc_output

# TypeScript cache
*.tsbuildinfo

# Uploads directory (keep structure but ignore content)
uploads/*
!uploads/.gitkeep
!uploads/extrait-video/
uploads/extrait-video/*
!uploads/extrait-video/.gitkeep

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

