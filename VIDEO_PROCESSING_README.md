# Video Processing API

This TypeScript backend provides comprehensive video processing capabilities using FFmpeg. The original JavaScript code has been successfully integrated and converted to TypeScript with proper type safety and error handling.

## Features

- ✅ **Video Cutting**: Cut video segments by specifying start and end times
- ✅ **Video Format Conversion**: Convert videos to different platform formats
- ✅ **MP4 Conversion**: Convert videos to MP4 format with optimized settings
- ✅ **Multiple Platform Support**: Horizontal, Vertical, Square, and Original formats
- ✅ **TypeScript Support**: Full type safety and IntelliSense support
- ✅ **Error Handling**: Comprehensive error handling and validation
- ✅ **FFmpeg Integration**: Uses system FFmpeg for optimal performance

## Prerequisites

- Node.js (v14 or higher)
- FFmpeg installed on the system
- TypeScript development environment

## Installation

1. Install dependencies:
   ```bash
   npm install
   ```

2. Install FFmpeg (if not already installed):
   ```bash
   # macOS
   brew install ffmpeg
   
   # Ubuntu/Debian
   sudo apt update && sudo apt install ffmpeg
   
   # Windows
   # Download from https://ffmpeg.org/download.html
   ```

3. Start the development server:
   ```bash
   npm run dev
   ```

## API Endpoints

### Health Check
- **GET** `/health` - Server health status

### Video Processing
- **GET** `/api/video` - Get video processing API information
- **POST** `/api/video/cut` - Cut video segments
- **POST** `/api/video/convert` - Convert video to different platform formats
- **POST** `/api/video/convert-to-mp4` - Convert video to MP4 format

## Usage Examples

### 1. Cut Video Segment

```bash
curl -X POST http://localhost:3000/api/video/cut \
  -H "Content-Type: application/json" \
  -d '{
    "folderName": "extrait-video",
    "filename": "input.mp4",
    "start": 10,
    "end": 30,
    "outputFolder": "output"
  }'
```

### 2. Convert Video Format

```bash
curl -X POST http://localhost:3000/api/video/convert \
  -H "Content-Type: application/json" \
  -d '{
    "inputVideoPath": "input.mp4",
    "outputVideoPath": "output.mp4",
    "platform": "square",
    "newFileName": "converted_video.mp4"
  }'
```

### 3. Convert to MP4

```bash
curl -X POST http://localhost:3000/api/video/convert-to-mp4 \
  -H "Content-Type: application/json" \
  -d '{
    "inputPath": "/path/to/input.avi",
    "outputPath": "/path/to/output.mp4"
  }'
```

## Supported Platforms

- **horizontal**: 1920x1080 (YouTube optimized)
- **vertical**: Cropped to square with enhanced brightness/saturation
- **square**: 1080x1080 with white padding
- **original**: Copy without modifications

## File Structure

```
src/
├── types/
│   └── video.ts              # TypeScript interfaces and types
├── services/
│   └── videoService.ts       # Video processing logic
├── routes/
│   ├── api.ts               # General API routes
│   └── video.ts             # Video processing routes
└── app.ts                   # Express app configuration

uploads/
├── extrait-video/           # Input video directory
└── [output-folders]/       # Generated output directories
```

## Error Handling

The API includes comprehensive error handling for:
- Missing required parameters
- Invalid file paths
- FFmpeg processing errors
- Invalid time ranges
- Unsupported video formats

## Development

- **Build**: `npm run build`
- **Development**: `npm run dev`
- **Production**: `npm start`

## Technical Details

- **Framework**: Express.js with TypeScript
- **Video Processing**: FFmpeg via fluent-ffmpeg
- **File Handling**: Node.js fs module
- **Validation**: Request parameter validation
- **Architecture**: Modular service-based architecture

## Migration Notes

The original JavaScript code has been successfully converted to TypeScript with the following improvements:

1. **Type Safety**: Added comprehensive TypeScript interfaces
2. **Error Handling**: Enhanced error handling and validation
3. **Modular Architecture**: Separated concerns into services and routes
4. **System FFmpeg**: Uses system FFmpeg instead of bundled binaries
5. **Express 4.x**: Downgraded to stable Express version for compatibility
6. **Promise-based**: Converted callback-based code to Promise-based for better async handling

## Troubleshooting

1. **FFmpeg not found**: Ensure FFmpeg is installed and available in PATH
2. **Permission errors**: Check file permissions for upload directories
3. **Memory issues**: For large videos, consider implementing streaming processing
4. **Architecture errors**: Ensure FFmpeg supports your system architecture

The video processing API is now fully functional and ready for production use!
