# Backend API - Node.js Express TypeScript

A modern backend API built with Node.js, Express, and TypeScript.

## Features

- ✅ TypeScript support
- ✅ Express.js framework
- ✅ CORS enabled
- ✅ Security headers with Helmet
- ✅ Request logging with Morgan
- ✅ Environment variables support
- ✅ Error handling middleware
- ✅ Health check endpoint
- ✅ API routes structure
- ✅ Development hot reload

## Prerequisites

- Node.js (v14 or higher)
- npm or yarn

## Installation

1. Navigate to the backend directory:
   ```bash
   cd backend
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Copy environment variables:
   ```bash
   cp .env.example .env
   ```

4. Update the `.env` file with your configuration.

## Available Scripts

- `npm run dev` - Start development server with hot reload
- `npm run build` - Build the TypeScript code
- `npm run start` - Start production server
- `npm run start:dev` - Start development server with ts-node
- `npm run clean` - Clean the dist directory
- `npm run build:watch` - Build in watch mode

## Development

Start the development server:

```bash
npm run dev
```

The server will start on `http://localhost:3000`

## API Endpoints

### Health Check
- `GET /health` - Server health status

### Root
- `GET /` - API information

### API Routes
- `GET /api` - API status and available endpoints
- `GET /api/users` - Get all users (example)
- `POST /api/users` - Create a new user (example)

## Project Structure

```
backend/
├── src/
│   ├── routes/
│   │   └── api.ts        # API routes
│   ├── app.ts            # Express app configuration
│   └── server.ts         # Server entry point
├── dist/                 # Built JavaScript files
├── .env                  # Environment variables
├── .env.example          # Environment variables example
├── .gitignore           # Git ignore rules
├── package.json         # Project dependencies and scripts
├── tsconfig.json        # TypeScript configuration
└── README.md            # This file
```

## Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `PORT` | Server port | 3000 |
| `NODE_ENV` | Environment | development |

## Error Handling

The API includes comprehensive error handling:

- 404 errors for undefined routes
- Global error handler for server errors
- Validation errors for API endpoints
- Environment-specific error messages

## Production Deployment

1. Build the project:
   ```bash
   npm run build
   ```

2. Start the production server:
   ```bash
   npm start
   ```

## Adding New Routes

1. Create a new route file in `src/routes/`
2. Import and use it in `src/app.ts`
3. Follow the existing pattern for consistent API responses

## License

ISC

