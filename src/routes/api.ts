import { Router, Request, Response } from 'express';

const router = Router();

// GET /api
router.get('/', (req: Request, res: Response) => {
  res.json({
    message: 'API is working!',
    version: '1.0.0',
    endpoints: [
      'GET /api',
      'GET /api/users',
      'POST /api/users'
    ]
  });
});
explain me thisconst ffmpeg = require("fluent-ffmpeg"); const ffmpegPath = require("@ffmpeg-installer/ffmpeg").path; ffmpeg.setFfmpegPath(ffmpegPath); const getVideoInfos = require("get-video-info"); const path = require("path"); const fs = require("fs");  //folder name = extrait-video //start ='00:00:03' //duration = 3' //cutAndSaveVideo("extrait-video", "3.mp4", 7, 19);   const ffprobePath = require("@ffprobe-installer/ffprobe").path; ffmpeg.setFfprobePath(ffprobePath);  ffmpeg.setFfmpegPath(ffmpegPath); ffmpeg.setFfprobePath(ffprobePath);    async function cutAndSaveVideo(folderName, filename, start, end, outputFolder) {   try {     console.log(outputFolder);     const outputFolderPath = `../../uploads/${outputFolder}`;      if (!fs.existsSync(outputFolderPath)) {       fs.mkdirSync(outputFolderPath, { recursive: true });     }      const outputFilename = new Date().getTime() + path.extname(filename);      const outputPath = path.join(__dirname, outputFolderPath, outputFilename);      const filePath = path.join(       __dirname,       "../../uploads",       folderName,       filename     );     if (!fs.existsSync(filePath)) {       throw new Error("File not found");     }      const fileInfo = await getVideoInfos(filePath);     let durationInSeconds = fileInfo.format.duration;      let duration = end - start;     if (start < 0 || start > durationInSeconds || end > durationInSeconds || duration <= 0) {       throw new Error('Invalid start time or end time');     }      await new Promise((resolve, reject) => {       ffmpeg(path.join(__dirname, '../../uploads', folderName, filename))         .setStartTime(start)         .setDuration(duration)         .output(outputPath)         .videoCodec('copy') // Copy video stream without re-encoding         .audioCodec('copy') // Copy audio stream without re-encoding         .addOption('-avoid_negative_ts', '1') // Avoid negative timestamps         .addOption('-movflags', 'faststart') // Optimize MP4 for streaming         .on('start', function (commandLine) {           console.log('Spawned Ffmpeg with command: ' + commandLine);           console.log('Processing');         })         .on('end', function () {           console.log('Conversion Done');           resolve();         })         .on('error', function (err) {           console.log('error: ', err);           reject(err);         })         .run();     });      return outputFilename;     // return path.join(outputFolder, outputFilename);   } catch (error) {     throw new Error(error.message);   } }  async function convertVideo(inputVideoPath, outputVideoPath, platform, newFileName) {   console.log("Converting video...");   console.log("Input video path:", inputVideoPath);   console.log("Output video path:", outputVideoPath);   console.log("Platform:", platform);   inputVideoPath = path.join(__dirname, "../../uploads/extrait-video", inputVideoPath);   outputVideoPath = path.join(__dirname, "../../uploads/extrait-video", outputVideoPath);   console.log("outputVideoPath outputVideoPath" + outputVideoPath)    return new Promise(async (resolve, reject) => {      const { mediaWidth, mediaHeight } = await getVideoDimensions(inputVideoPath);     console.log(mediaWidth, mediaHeight);      try {       if (platform === "horizontal") {         if (mediaWidth && mediaHeight) {           let paddingX, paddingY;            // YouTube's recommended horizontal resolution is 1920x1080           const desiredWidth = 1920;           const desiredHeight = 1080;           const desiredAspectRatio = desiredWidth / desiredHeight;            // Scale the input video to the desired resolution (1920x1080)           const inputAspectRatio = mediaWidth / mediaHeight;           if (inputAspectRatio > desiredAspectRatio) {             // Landscape video             paddingY = 0;             paddingX = Math.floor((desiredHeight * inputAspectRatio - desiredWidth) / 2);           } else {             // Portrait video             paddingX = 0;             paddingY = Math.floor((desiredWidth / inputAspectRatio - desiredHeight) / 2);           }           ffmpeg(inputVideoPath)             .output(outputVideoPath)             .videoCodec('copy') // Copy video stream without re-encoding             .audioCodec('copy') // Copy audio stream without re-encoding             .addOption('-avoid_negative_ts', '1') // Avoid negative timestamps             .addOption('-movflags', 'faststart') // Optimize MP4 for streaming             .on("start", (commandLine) => {               console.log("Spawned FFmpeg with command:", commandLine);               console.log("Converting video...");             })             .on("end", () => {               console.log("Video conversion complete!");               resolve(newFileName); // Resolve with the outputVideoPath             })             .on("error", (err) => {               console.error("Error converting video:", err);               reject(err);             })             .run();          }       } else if (platform === "vertical") {         console.log("Vertical video processing...", platform);         if (mediaWidth && mediaHeight) {           console.log("Square video processing...", mediaWidth, mediaHeight);           const desiredSize = Math.min(mediaWidth, mediaHeight); // Choose the smaller dimension            const cropWidth = desiredSize;           const cropHeight = desiredSize;           const cropFromX = Math.floor((mediaWidth - cropWidth) / 2);           const cropFromY = Math.floor((mediaHeight - cropHeight) / 2);            ffmpeg(inputVideoPath)             .output(outputVideoPath)             .videoFilter([               {                 filter: "crop",                 options: {                   w: cropWidth,                   h: cropHeight,                   x: cropFromX,                   y: cropFromY,                 },               },               {                 filter: "eq",                 options: "brightness=0.1:saturation=2",               },             ])             .videoCodec('libx264') // Encode video with libx264 codec             .audioCodec('copy') // Copy audio stream without re-encoding (optional)             .addOption('-avoid_negative_ts', '1') // Avoid negative timestamps             .addOption('-movflags', 'faststart') // Optimize MP4 for streaming             .on("start", (commandLine) => {               console.log("Spawned FFmpeg with command:", commandLine);               console.log("Converting video...");             })             .on("end", async () => {               console.log("Video conversion complete!");               resolve(newFileName); // Resolve with the outputVideoPath             })             .on("error", (err) => {               console.error("Error converting video:", err);               reject(err);             })             .run();         }       }       else if (platform === "original") {         if (mediaWidth && mediaHeight) {           console.log("Original video processing...");            ffmpeg(inputVideoPath)             .output(outputVideoPath)             .videoCodec('copy') // Copy video stream without re-encoding             .audioCodec('copy') // Copy audio stream without re-encoding             .addOption('-avoid_negative_ts', '1') // Avoid negative timestamps             .addOption('-movflags', 'faststart') // Optimize MP4 for streaming             .on("start", (commandLine) => {               console.log("Spawned FFmpeg with command:", commandLine);               console.log("Copying original video...");             })             .on("end", () => {               console.log("Video conversion complete!");               resolve(newFileName); // Resolve with the outputVideoPath             })             .on("error", (err) => {               console.error("Error copying original video:", err);               reject(err);             })             .run();         } else {           console.log("Video dimensions not detected!");         }         } else if (platform === "square") {         if (mediaWidth && mediaHeight) {           let newWidth, newHeight, paddingX, paddingY;            const desiredWidth = 1080;           const desiredHeight = 1080;           const desiredAspectRatio = desiredWidth / desiredHeight;            // Calculate the dimensions to maintain the input aspect ratio           const inputAspectRatio = mediaWidth / mediaHeight;           if (inputAspectRatio > desiredAspectRatio) {             // Landscape video             newWidth = desiredWidth;             newHeight = Math.round(desiredWidth / inputAspectRatio);             paddingY = Math.floor((desiredHeight - newHeight) / 2);             paddingX = 0;           } else {             // Portrait video             newWidth = Math.round(desiredHeight * inputAspectRatio);             newHeight = desiredHeight;             paddingX = Math.floor((desiredWidth - newWidth) / 2);             paddingY = 0;           }            ffmpeg(inputVideoPath)             .output(outputVideoPath)             .videoFilters([               `scale=${newWidth}:${newHeight}`, // Scale to maintain aspect ratio               `pad=${desiredWidth}:${desiredHeight}:${paddingX}:${paddingY}:white`, // Add padding to fit TikTok format             ])             .audioCodec('copy')             .addOption('-avoid_negative_ts', '1')             .addOption('-movflags', 'faststart')             .addOption('-preset', 'fast') // Faster encoding preset             .addOption('-profile:v', 'main') // Main profile for compatibility             .addOption('-crf', '23') // Constant Rate Factor for quality (adjust as needed)             .on("start", (commandLine) => {               console.log("Spawned FFmpeg with command:", commandLine);               console.log("Converting video...");             })             .on("end", () => {               console.log("Video conversion complete!");               resolve(newFileName); // Resolve with the outputVideoPath              })             .on("error", (err) => {               console.error("Error converting video:", err);             })             .run();         }        }       //  else if (platform === "square") {       //   if (mediaWidth && mediaHeight) {       //     // TikTok's recommended resolution (adjust if needed)       //     const desiredWidth = 1080;       //     const desiredHeight = 1920;       //     const desiredAspectRatio = desiredWidth / desiredHeight;        //     // Calculate dimensions to maintain input aspect ratio       //     const inputAspectRatio = mediaWidth / mediaHeight;       //     let newWidth, newHeight, paddingX, paddingY;       //     if (inputAspectRatio > desiredAspectRatio) {       //       // Landscape video       //       newWidth = desiredWidth;       //       newHeight = Math.round(desiredWidth / inputAspectRatio);       //       paddingY = Math.floor((desiredHeight - newHeight) / 2);       //       paddingX = 0;       //     } else {       //       // Portrait video       //       newWidth = Math.round(desiredHeight * inputAspectRatio);       //       newHeight = desiredHeight;       //       paddingX = Math.floor((desiredWidth - newWidth) / 2);       //       paddingY = 0;       //     }        //     ffmpeg(inputVideoPath)       //       .output(outputVideoPath)       //       .videoFilters([       //         `scale=${newWidth}:${newHeight}`,       //         `pad=${desiredWidth}:${desiredHeight}:${paddingX}:${paddingY}:black`       //       ])       //       .videoCodec('h264_nvenc') // Utilize NVIDIA GPU acceleration       //       .audioCodec('copy')       //       .addOption('-avoid_negative_ts', '1')       //       .addOption('-movflags', 'faststart')       //       .addOption('-preset', 'fast') // Faster encoding preset       //       .addOption('-profile:v', 'main') // Main profile for compatibility       //       .addOption('-crf', '23') // Constant Rate Factor for quality (adjust as needed)       //       .on("start", (commandLine) => {       //         console.log("Spawned FFmpeg with command:", commandLine);       //         console.log("Converting video...");       //       })       //       .on("end", () => {       //         console.log("Video conversion complete!");       //         resolve();       //       })       //       .on("error", (err) => {       //         console.error("Error converting video:", err);       //         reject(err);       //       })       //       .run();       //   }       // }       else {         resolve(new Error("Video dimensions not detected!"));       }     } catch (err) {       console.error("Error:", err);       reject(err);     }   }); }  function getVideoDimensions(videoPath) {   return new Promise((resolve, reject) => {     ffmpeg.ffprobe(videoPath, (err, metadata) => {       if (err) {         console.log("Error getting video dimensions:", err);         reject(err);       } else {         console.log("Metadata:", metadata.streams[0].width, metadata.streams[0].height);         const mediaWidth = metadata.streams[0].width;         const mediaHeight = metadata.streams[0].height;         resolve({ mediaWidth, mediaHeight });       }     });   }); }  function convertToMp4(inputPath, outputPath) {   return new Promise((resolve, reject) => {     ffmpeg(inputPath)       .output(outputPath)       .videoCodec('libx264') // Use H.264 codec       .audioCodec('aac') // Use AAC codec for audio       .outputOptions([         '-preset fast', // Faster encoding preset         '-crf 23', // Constant rate factor for quality control         '-movflags +faststart' // Enables fast start for MP4       ])       .on('end', () => {         console.log('Conversion finished');         resolve(outputPath);       })       .on('error', (err) => {         console.error('Conversion error: ', err);         reject(err);       })       .run();   }); }          module.exports = { cutAndSaveVideo, convertVideo, convertToMp4 };
export default router;

