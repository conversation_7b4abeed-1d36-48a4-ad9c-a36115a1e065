import { Router, Request, Response } from 'express';

const router = Router();

// GET /api
router.get('/', (req: Request, res: Response) => {
  res.json({
    message: 'API is working!',
    version: '1.0.0',
    endpoints: [
      'GET /api',
      'GET /api/video',
      'POST /api/video/cut',
      'POST /api/video/convert',
      'POST /api/video/convert-to-mp4'
    ]
  });
});

export default router;

