import { Router, Request, Response, NextFunction } from 'express';
import { cutAndSaveVideo, convertVideo, convertToMp4 } from '../services/videoService';
import { Platform } from '../types/video';
import Busboy from 'busboy';
import * as fs from 'fs';
import * as path from 'path';

const router = Router();

// POST /api/video/cut - Cut and save video segment
router.post('/cut', (req: Request, res: Response, next: NextFunction) => {
  const { folderName, filename, start, end, outputFolder } = req.body;

  // Validate required parameters
  if (!folderName || !filename || start === undefined || end === undefined || !outputFolder) {
    return res.status(400).json({
      error: 'Bad Request',
      message: 'Missing required parameters: folderName, filename, start, end, outputFolder'
    });
  }

  // Validate numeric parameters
  if (typeof start !== 'number' || typeof end !== 'number') {
    return res.status(400).json({
      error: 'Bad Request',
      message: 'start and end must be numbers'
    });
  }

  if (start >= end) {
    return res.status(400).json({
      error: 'Bad Request',
      message: 'start time must be less than end time'
    });
  }

  cutAndSaveVideo(folderName, filename, start, end, outputFolder)
    .then((outputFilename) => {
      res.json({
        success: true,
        message: 'Video cut successfully',
        data: {
          outputFilename,
          outputFolder,
          duration: end - start
        }
      });
    })
    .catch((error) => {
      console.error('Error cutting video:', error);
      res.status(500).json({
        error: 'Internal Server Error',
        message: (error as Error).message
      });
    });

  return;
});

// POST /api/video/convert - Convert video to different platform formats
router.post('/convert', (req: Request, res: Response) => {
  const { inputVideoPath, outputVideoPath, platform, newFileName } = req.body;

  // Validate required parameters
  if (!inputVideoPath || !outputVideoPath || !platform || !newFileName) {
    return res.status(400).json({
      error: 'Bad Request',
      message: 'Missing required parameters: inputVideoPath, outputVideoPath, platform, newFileName'
    });
  }

  // Validate platform parameter
  const validPlatforms: Platform[] = ['horizontal', 'vertical', 'original', 'square'];
  if (!validPlatforms.includes(platform)) {
    return res.status(400).json({
      error: 'Bad Request',
      message: `Invalid platform. Must be one of: ${validPlatforms.join(', ')}`
    });
  }

  convertVideo(inputVideoPath, outputVideoPath, platform, newFileName)
    .then((result) => {
      res.json({
        success: true,
        message: 'Video converted successfully',
        data: {
          outputFilename: result,
          platform,
          inputPath: inputVideoPath,
          outputPath: outputVideoPath
        }
      });
    })
    .catch((error) => {
      console.error('Error converting video:', error);
      res.status(500).json({
        error: 'Internal Server Error',
        message: (error as Error).message
      });
    });

  return;
});

// POST /api/video/convert-to-mp4 - Convert video to MP4 format
router.post('/convert-to-mp4', (req: Request, res: Response) => {
  const { inputPath, outputPath } = req.body;

  // Validate required parameters
  if (!inputPath || !outputPath) {
    return res.status(400).json({
      error: 'Bad Request',
      message: 'Missing required parameters: inputPath, outputPath'
    });
  }

  convertToMp4(inputPath, outputPath)
    .then((result) => {
      res.json({
        success: true,
        message: 'Video converted to MP4 successfully',
        data: {
          outputPath: result,
          inputPath
        }
      });
    })
    .catch((error) => {
      console.error('Error converting video to MP4:', error);
      res.status(500).json({
        error: 'Internal Server Error',
        message: (error as Error).message
      });
    });

  return;
});

// POST /api/video/upload-and-cut - Upload video and cut segment
router.post('/upload-and-cut', (req: Request, res: Response) => {
  const busboy = Busboy({ headers: req.headers });
  let uploadedFilePath = '';
  let formData: any = {};

  // Handle file upload
  busboy.on('file', (fieldname: string, file: NodeJS.ReadableStream, info: any): void => {
    const { filename, mimeType } = info;

    if (!filename || !mimeType.startsWith('video/')) {
      res.status(400).json({
        error: 'Bad Request',
        message: 'Please upload a valid video file'
      });
      return;
    }

    // Create unique filename
    const timestamp = new Date().getTime();
    const uniqueFilename = `${timestamp}_${filename}`;
    uploadedFilePath = path.join(process.cwd(), 'uploads', 'extrait-video', uniqueFilename);

    // Save uploaded file
    const writeStream = fs.createWriteStream(uploadedFilePath);
    file.pipe(writeStream);

    writeStream.on('error', (error) => {
      console.error('Error saving file:', error);
      res.status(500).json({
        error: 'Internal Server Error',
        message: 'Failed to save uploaded file'
      });
    });
  });

  // Handle form fields
  busboy.on('field', (fieldname: string, value: string) => {
    formData[fieldname] = value;
  });

  // Handle completion
  busboy.on('finish', async (): Promise<void> => {
    try {
      const { start, end, outputFolder } = formData;

      // Validate required parameters
      if (!start || !end || !outputFolder) {
        res.status(400).json({
          error: 'Bad Request',
          message: 'Missing required parameters: start, end, outputFolder'
        });
        return;
      }

      const startTime = parseFloat(start);
      const endTime = parseFloat(end);

      if (isNaN(startTime) || isNaN(endTime) || startTime >= endTime) {
        res.status(400).json({
          error: 'Bad Request',
          message: 'Invalid start or end time'
        });
        return;
      }

      // Wait a bit for file to be fully written
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Process video cutting
      const filename = path.basename(uploadedFilePath);
      const outputFilename = await cutAndSaveVideo('extrait-video', filename, startTime, endTime, outputFolder);

      res.json({
        success: true,
        message: 'Video uploaded and cut successfully',
        data: {
          originalFilename: filename,
          outputFilename,
          outputFolder,
          duration: endTime - startTime,
          uploadedFile: uploadedFilePath
        }
      });
    } catch (error) {
      console.error('Error processing video:', error);
      res.status(500).json({
        error: 'Internal Server Error',
        message: (error as Error).message
      });
    }
  });

  req.pipe(busboy);
});

// POST /api/video/upload-and-convert - Upload video and convert to platform format
router.post('/upload-and-convert', (req: Request, res: Response) => {
  const busboy = Busboy({ headers: req.headers });
  let uploadedFilePath = '';
  let formData: any = {};

  // Handle file upload
  busboy.on('file', (_fieldname: string, file: NodeJS.ReadableStream, info: any): void => {
    const { filename, mimeType } = info;

    if (!filename || !mimeType.startsWith('video/')) {
      res.status(400).json({
        error: 'Bad Request',
        message: 'Please upload a valid video file'
      });
      return;
    }

    // Create unique filename
    const timestamp = new Date().getTime();
    const uniqueFilename = `${timestamp}_${filename}`;
    uploadedFilePath = path.join(process.cwd(), 'uploads', 'extrait-video', uniqueFilename);

    // Save uploaded file
    const writeStream = fs.createWriteStream(uploadedFilePath);
    file.pipe(writeStream);

    writeStream.on('error', (error) => {
      console.error('Error saving file:', error);
      res.status(500).json({
        error: 'Internal Server Error',
        message: 'Failed to save uploaded file'
      });
    });
  });

  // Handle form fields
  busboy.on('field', (fieldname: string, value: string) => {
    formData[fieldname] = value;
  });

  // Handle completion
  busboy.on('finish', async (): Promise<void> => {
    try {
      const { platform } = formData;

      // Validate required parameters
      if (!platform) {
        res.status(400).json({
          error: 'Bad Request',
          message: 'Missing required parameter: platform'
        });
        return;
      }

      // Validate platform parameter
      const validPlatforms: Platform[] = ['horizontal', 'vertical', 'original', 'square'];
      if (!validPlatforms.includes(platform)) {
        res.status(400).json({
          error: 'Bad Request',
          message: `Invalid platform. Must be one of: ${validPlatforms.join(', ')}`
        });
        return;
      }

      // Wait a bit for file to be fully written
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Process video conversion
      const inputFilename = path.basename(uploadedFilePath);
      const timestamp = new Date().getTime();
      const outputFilename = `${timestamp}_converted_${platform}${path.extname(inputFilename)}`;

      const result = await convertVideo(inputFilename, outputFilename, platform, outputFilename);

      res.json({
        success: true,
        message: 'Video uploaded and converted successfully',
        data: {
          originalFilename: inputFilename,
          outputFilename: result,
          platform,
          uploadedFile: uploadedFilePath
        }
      });
    } catch (error) {
      console.error('Error processing video:', error);
      res.status(500).json({
        error: 'Internal Server Error',
        message: (error as Error).message
      });
    }
  });

  req.pipe(busboy);
});

// GET /api/video - Get video processing endpoints info
router.get('/', (_req: Request, res: Response) => {
  res.json({
    message: 'Video Processing API',
    version: '1.0.0',
    endpoints: [
      'GET /api/video',
      'POST /api/video/cut',
      'POST /api/video/convert',
      'POST /api/video/convert-to-mp4',
      'POST /api/video/upload-and-cut',
      'POST /api/video/upload-and-convert'
    ],
    supportedPlatforms: ['horizontal', 'vertical', 'original', 'square'],
    uploadEndpoints: {
      'upload-and-cut': 'Upload video file and cut segment',
      'upload-and-convert': 'Upload video file and convert to platform format'
    }
  });
});

export default router;
