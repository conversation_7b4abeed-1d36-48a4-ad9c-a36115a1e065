import { Router, Request, Response } from 'express';
import { processVideoStream } from '../services/videoService';
import { Platform } from '../types/video';
import Busboy from 'busboy';
import * as fs from 'fs';
import * as path from 'path';

const router = Router();



/**
 * @swagger
 * /api/video/upload-and-cut-download:
 *   post:
 *     tags:
 *       - Video Processing
 *     summary: Upload, process, and download video directly
 *     description: |
 *       Upload a video file, cut a segment, optionally convert to different platform formats,
 *       and download the processed video directly without storing it on the server.
 *
 *       **Features:**
 *       - Cut video segments by specifying start and end times
 *       - Convert to platform-specific formats (YouTube, TikTok, Instagram)
 *       - Apply custom padding colors (named colors, hex, RGB, HSL)
 *       - Direct download without server storage
 *       - Automatic cleanup of temporary files
 *
 *       **Supported Platforms:**
 *       - `horizontal`: 1920x1080 (YouTube optimized)
 *       - `vertical`: 1080x1920 (TikTok/Stories optimized)
 *       - `square`: 1080x1080 (Instagram optimized)
 *       - `original`: No format changes
 *
 *       **Color Formats:**
 *       - Named: `white`, `black`, `red`, `green`, `blue`, `yellow`, `gray`, `transparent`
 *       - Hex: `#FF0000`, `#00FF00`, `#0000FF`
 *       - RGB: `rgb(255,0,0)`, `rgba(255,0,0,0.5)`
 *       - HSL: `hsl(0,100%,50%)`, `hsla(0,100%,50%,0.8)`
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             $ref: '#/components/schemas/VideoProcessingRequest'
 *           examples:
 *             basicCut:
 *               summary: Basic video cut
 *               value:
 *                 video: "[Upload your video file]"
 *                 start: 5
 *                 end: 15
 *             instagramSquare:
 *               summary: Instagram square with custom color
 *               value:
 *                 video: "[Upload your video file]"
 *                 start: 0
 *                 end: 30
 *                 platform: "square"
 *                 paddingColor: "#FFA500"
 *             tiktokVertical:
 *               summary: TikTok vertical format
 *               value:
 *                 video: "[Upload your video file]"
 *                 start: 10
 *                 end: 25
 *                 platform: "vertical"
 *                 paddingColor: "rgb(255,100,150)"
 *     responses:
 *       200:
 *         description: Video processed successfully - file download starts immediately
 *         content:
 *           video/mp4:
 *             schema:
 *               type: string
 *               format: binary
 *         headers:
 *           Content-Disposition:
 *             description: Attachment with generated filename
 *             schema:
 *               type: string
 *               example: 'attachment; filename="processed_video_1750330000000.mp4"'
 *           Content-Type:
 *             description: Video MIME type
 *             schema:
 *               type: string
 *               example: 'video/mp4'
 *       400:
 *         description: Bad request - invalid parameters
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             examples:
 *               missingParams:
 *                 summary: Missing required parameters
 *                 value:
 *                   error: "Bad Request"
 *                   message: "Missing required parameters: start, end"
 *               invalidTime:
 *                 summary: Invalid time range
 *                 value:
 *                   error: "Bad Request"
 *                   message: "Invalid start or end time"
 *               invalidPlatform:
 *                 summary: Invalid platform
 *                 value:
 *                   error: "Bad Request"
 *                   message: "Invalid platform. Must be one of: horizontal, vertical, original, square"
 *               invalidFile:
 *                 summary: Invalid file type
 *                 value:
 *                   error: "Bad Request"
 *                   message: "Please upload a valid video file"
 *       500:
 *         description: Internal server error - processing failed
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               error: "Internal Server Error"
 *               message: "Video processing failed"
 */
// POST /api/video/upload-and-cut-download - Upload video, process, and download directly
router.post('/upload-and-cut-download', (req: Request, res: Response) => {
  const busboy = Busboy({ headers: req.headers });
  let uploadedFilePath = '';
  let formData: any = {};

  // Handle file upload
  busboy.on('file', (_fieldname: string, file: NodeJS.ReadableStream, info: any): void => {
    const { filename, mimeType } = info;

    if (!filename || !mimeType.startsWith('video/')) {
      res.status(400).json({
        error: 'Bad Request',
        message: 'Please upload a valid video file'
      });
      return;
    }

    // Create unique filename
    const timestamp = new Date().getTime();
    const uniqueFilename = `${timestamp}_${filename}`;
    uploadedFilePath = path.join(process.cwd(), 'uploads', 'extrait-video', uniqueFilename);

    // Save uploaded file
    const writeStream = fs.createWriteStream(uploadedFilePath);
    file.pipe(writeStream);

    writeStream.on('error', (error) => {
      console.error('Error saving file:', error);
      res.status(500).json({
        error: 'Internal Server Error',
        message: 'Failed to save uploaded file'
      });
    });
  });

  // Handle form fields
  busboy.on('field', (fieldname: string, value: string) => {
    formData[fieldname] = value;
  });

  // Handle completion
  busboy.on('finish', async (): Promise<void> => {
    try {
      const { start, end, platform, paddingColor } = formData;

      // Validate required parameters
      if (!start || !end) {
        res.status(400).json({
          error: 'Bad Request',
          message: 'Missing required parameters: start, end'
        });
        return;
      }

      const startTime = parseFloat(start);
      const endTime = parseFloat(end);

      if (isNaN(startTime) || isNaN(endTime) || startTime >= endTime) {
        res.status(400).json({
          error: 'Bad Request',
          message: 'Invalid start or end time'
        });
        return;
      }

      // Validate platform parameter if provided
      let platformType: Platform | undefined;
      if (platform) {
        const validPlatforms: Platform[] = ['horizontal', 'vertical', 'original', 'square'];
        if (!validPlatforms.includes(platform)) {
          res.status(400).json({
            error: 'Bad Request',
            message: `Invalid platform. Must be one of: ${validPlatforms.join(', ')}`
          });
          return;
        }
        platformType = platform;
      }

      // Convert color to FFmpeg-compatible format
      // FFmpeg only accepts named colors or hex codes in pad filter
      const convertToFFmpegColor = (color: string): string => {
        if (!color) return 'white';

        const namedColors = ['white', 'black', 'red', 'green', 'blue', 'yellow', 'gray', 'transparent'];
        const lowerColor = color.toLowerCase().trim();

        // Check if it's a valid named color
        if (namedColors.includes(lowerColor)) {
          return lowerColor;
        }

        // Check if it's a valid hex color code (#RRGGBB or #RGB)
        const hexColorRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/;
        if (hexColorRegex.test(color)) {
          return color;
        }

        // Convert RGB to hex
        const rgbRegex = /^rgba?\(\s*(\d{1,3})\s*,\s*(\d{1,3})\s*,\s*(\d{1,3})\s*(?:,\s*(0|1|0?\.\d+))?\s*\)$/i;
        const rgbMatch = color.match(rgbRegex);
        if (rgbMatch) {
          const [, r, g, b] = rgbMatch;
          const red = parseInt(r);
          const green = parseInt(g);
          const blue = parseInt(b);

          // Validate RGB values are in range 0-255
          if (red >= 0 && red <= 255 && green >= 0 && green <= 255 && blue >= 0 && blue <= 255) {
            // Convert to hex (ignore alpha for now as FFmpeg pad doesn't support it)
            const hex = `#${red.toString(16).padStart(2, '0')}${green.toString(16).padStart(2, '0')}${blue.toString(16).padStart(2, '0')}`;
            return hex.toUpperCase();
          }
        }

        // Convert HSL to hex
        const hslRegex = /^hsla?\(\s*(\d{1,3})\s*,\s*(\d{1,3})%\s*,\s*(\d{1,3})%\s*(?:,\s*(0|1|0?\.\d+))?\s*\)$/i;
        const hslMatch = color.match(hslRegex);
        if (hslMatch) {
          const [, h, s, l] = hslMatch;
          const hue = parseInt(h) / 360;
          const saturation = parseInt(s) / 100;
          const lightness = parseInt(l) / 100;

          // Validate HSL values are in range
          if (hue >= 0 && hue <= 1 && saturation >= 0 && saturation <= 1 && lightness >= 0 && lightness <= 1) {
            // Convert HSL to RGB
            const hslToRgb = (h: number, s: number, l: number) => {
              let r, g, b;

              if (s === 0) {
                r = g = b = l; // achromatic
              } else {
                const hue2rgb = (p: number, q: number, t: number) => {
                  if (t < 0) t += 1;
                  if (t > 1) t -= 1;
                  if (t < 1/6) return p + (q - p) * 6 * t;
                  if (t < 1/2) return q;
                  if (t < 2/3) return p + (q - p) * (2/3 - t) * 6;
                  return p;
                };

                const q = l < 0.5 ? l * (1 + s) : l + s - l * s;
                const p = 2 * l - q;
                r = hue2rgb(p, q, h + 1/3);
                g = hue2rgb(p, q, h);
                b = hue2rgb(p, q, h - 1/3);
              }

              return [Math.round(r * 255), Math.round(g * 255), Math.round(b * 255)];
            };

            const [red, green, blue] = hslToRgb(hue, saturation, lightness);
            const hex = `#${red.toString(16).padStart(2, '0')}${green.toString(16).padStart(2, '0')}${blue.toString(16).padStart(2, '0')}`;
            return hex.toUpperCase();
          }
        }

        // Default to white if invalid
        return 'white';
      };

      const selectedPaddingColor = convertToFFmpegColor(paddingColor);

      // Wait a bit for file to be fully written
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Process and stream video directly to user
      const filename = path.basename(uploadedFilePath);
      console.log(`Processing video: ${filename}, start: ${startTime}, end: ${endTime}, platform: ${platformType || 'none'}`);

      await processVideoStream(
        uploadedFilePath,
        startTime,
        endTime,
        platformType,
        selectedPaddingColor,
        res
      );

      // Clean up uploaded file after streaming
      try {
        fs.unlinkSync(uploadedFilePath);
        console.log('Cleaned up uploaded file:', uploadedFilePath);
      } catch (cleanupError) {
        console.error('Error cleaning up uploaded file:', cleanupError);
      }
    } catch (error) {
      console.error('Error processing video:', error);
      res.status(500).json({
        error: 'Internal Server Error',
        message: (error as Error).message
      });
    }
  });

  req.pipe(busboy);
});



/**
 * @swagger
 * /api/video:
 *   get:
 *     tags:
 *       - API Info
 *     summary: Get API information and documentation
 *     description: |
 *       Returns comprehensive information about the Video Processing API including:
 *       - Available endpoints and their descriptions
 *       - Supported platforms and their specifications
 *       - Parameter requirements and examples
 *       - Color format options and examples
 *       - Usage instructions and examples
 *     responses:
 *       200:
 *         description: API information retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ApiInfo'
 *             example:
 *               message: "Video Processing API - Streamlined Edition"
 *               version: "2.1.0"
 *               description: "Upload, process, and download videos directly without server storage"
 *               mainEndpoint: "POST /api/video/upload-and-cut-download"
 *               supportedPlatforms: ["horizontal", "vertical", "original", "square"]
 *               parameters:
 *                 required: ["video (file)", "start (number)", "end (number)"]
 *                 optional: ["platform (string)", "paddingColor (string)"]
 */
// GET /api/video - Get video processing endpoints info
router.get('/', (_req: Request, res: Response) => {
  res.json({
    message: 'Video Processing API - Streamlined Edition',
    version: '2.0.0',
    description: 'Upload, process, and download videos directly without server storage',
    mainEndpoint: 'POST /api/video/upload-and-cut-download',
    endpoints: [
      'GET /api/video - API information',
      'POST /api/video/upload-and-cut-download - Main endpoint: Upload, process, and download directly'
    ],
    supportedPlatforms: ['horizontal', 'vertical', 'original', 'square'],
    platformDescriptions: {
      horizontal: '1920x1080 (YouTube) - Entire video visible with customizable padding',
      vertical: '1080x1920 (TikTok/Stories) - Entire video visible with customizable padding',
      square: '1080x1080 (Instagram) - Entire video visible with customizable padding',
      original: 'Original dimensions - No changes to resolution or aspect ratio'
    },
    parameters: {
      required: [
        'video (file) - Video file to process',
        'start (number) - Start time in seconds',
        'end (number) - End time in seconds'
      ],
      optional: [
        'platform (string) - Target format: horizontal|vertical|original|square',
        'paddingColor (string) - Padding color in any format (default: white)'
      ]
    },
    paddingColorFormats: {
      namedColors: ['white', 'black', 'red', 'green', 'blue', 'yellow', 'gray', 'transparent'],
      hexCodes: ['#FF0000', '#00FF00', '#0000FF', '#FFA500'],
      rgbColors: ['rgb(255,0,0)', 'rgb(0,255,0)', 'rgba(255,0,0,0.5)'],
      hslColors: ['hsl(0,100%,50%)', 'hsl(120,100%,50%)', 'hsla(240,100%,50%,0.8)']
    },
    usage: {
      method: 'POST',
      url: '/api/video/upload-and-cut-download',
      contentType: 'multipart/form-data',
      response: 'Direct file download (video/mp4)',
      privacy: 'No files stored on server - processed and streamed directly'
    },
    examples: [
      {
        name: 'Basic Cut',
        description: 'Cut 10 seconds from video',
        parameters: {
          video: '[file]',
          start: 5,
          end: 15
        }
      },
      {
        name: 'Square with Custom Color',
        description: 'Create Instagram-ready square video with orange padding',
        parameters: {
          video: '[file]',
          start: 0,
          end: 30,
          platform: 'square',
          paddingColor: '#FFA500'
        }
      },
      {
        name: 'Vertical TikTok',
        description: 'Create vertical video for TikTok/Stories',
        parameters: {
          video: '[file]',
          start: 10,
          end: 25,
          platform: 'vertical',
          paddingColor: 'black'
        }
      }
    ]
  });
});

export default router;
