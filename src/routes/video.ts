import { Router, Request, Response, NextFunction } from 'express';
import { cutAndSaveVideo, convertVideo, convertToMp4 } from '../services/videoService';
import { Platform } from '../types/video';

const router = Router();

// POST /api/video/cut - Cut and save video segment
router.post('/cut', (req: Request, res: Response, next: NextFunction) => {
  const { folderName, filename, start, end, outputFolder } = req.body;

  // Validate required parameters
  if (!folderName || !filename || start === undefined || end === undefined || !outputFolder) {
    return res.status(400).json({
      error: 'Bad Request',
      message: 'Missing required parameters: folderName, filename, start, end, outputFolder'
    });
  }

  // Validate numeric parameters
  if (typeof start !== 'number' || typeof end !== 'number') {
    return res.status(400).json({
      error: 'Bad Request',
      message: 'start and end must be numbers'
    });
  }

  if (start >= end) {
    return res.status(400).json({
      error: 'Bad Request',
      message: 'start time must be less than end time'
    });
  }

  cutAndSaveVideo(folderName, filename, start, end, outputFolder)
    .then((outputFilename) => {
      res.json({
        success: true,
        message: 'Video cut successfully',
        data: {
          outputFilename,
          outputFolder,
          duration: end - start
        }
      });
    })
    .catch((error) => {
      console.error('Error cutting video:', error);
      res.status(500).json({
        error: 'Internal Server Error',
        message: (error as Error).message
      });
    });

  return;
});

// POST /api/video/convert - Convert video to different platform formats
router.post('/convert', (req: Request, res: Response) => {
  const { inputVideoPath, outputVideoPath, platform, newFileName } = req.body;

  // Validate required parameters
  if (!inputVideoPath || !outputVideoPath || !platform || !newFileName) {
    return res.status(400).json({
      error: 'Bad Request',
      message: 'Missing required parameters: inputVideoPath, outputVideoPath, platform, newFileName'
    });
  }

  // Validate platform parameter
  const validPlatforms: Platform[] = ['horizontal', 'vertical', 'original', 'square'];
  if (!validPlatforms.includes(platform)) {
    return res.status(400).json({
      error: 'Bad Request',
      message: `Invalid platform. Must be one of: ${validPlatforms.join(', ')}`
    });
  }

  convertVideo(inputVideoPath, outputVideoPath, platform, newFileName)
    .then((result) => {
      res.json({
        success: true,
        message: 'Video converted successfully',
        data: {
          outputFilename: result,
          platform,
          inputPath: inputVideoPath,
          outputPath: outputVideoPath
        }
      });
    })
    .catch((error) => {
      console.error('Error converting video:', error);
      res.status(500).json({
        error: 'Internal Server Error',
        message: (error as Error).message
      });
    });

  return;
});

// POST /api/video/convert-to-mp4 - Convert video to MP4 format
router.post('/convert-to-mp4', (req: Request, res: Response) => {
  const { inputPath, outputPath } = req.body;

  // Validate required parameters
  if (!inputPath || !outputPath) {
    return res.status(400).json({
      error: 'Bad Request',
      message: 'Missing required parameters: inputPath, outputPath'
    });
  }

  convertToMp4(inputPath, outputPath)
    .then((result) => {
      res.json({
        success: true,
        message: 'Video converted to MP4 successfully',
        data: {
          outputPath: result,
          inputPath
        }
      });
    })
    .catch((error) => {
      console.error('Error converting video to MP4:', error);
      res.status(500).json({
        error: 'Internal Server Error',
        message: (error as Error).message
      });
    });

  return;
});

// GET /api/video - Get video processing endpoints info
router.get('/', (req: Request, res: Response) => {
  res.json({
    message: 'Video Processing API',
    version: '1.0.0',
    endpoints: [
      'GET /api/video',
      'POST /api/video/cut',
      'POST /api/video/convert',
      'POST /api/video/convert-to-mp4'
    ],
    supportedPlatforms: ['horizontal', 'vertical', 'original', 'square']
  });
});

export default router;
