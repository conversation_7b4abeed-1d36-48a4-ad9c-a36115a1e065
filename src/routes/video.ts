import { Router, Request, Response } from 'express';
import { cutAndSaveVideo, convertVideo, convertToMp4, processVideoStream } from '../services/videoService';
import { Platform } from '../types/video';
import Busboy from 'busboy';
import * as fs from 'fs';
import * as path from 'path';

const router = Router();

// POST /api/video/cut - Cut and save video segment
router.post('/cut', (req: Request, res: Response) => {
  const { folderName, filename, start, end, outputFolder } = req.body;

  // Validate required parameters
  if (!folderName || !filename || start === undefined || end === undefined || !outputFolder) {
    return res.status(400).json({
      error: 'Bad Request',
      message: 'Missing required parameters: folderName, filename, start, end, outputFolder'
    });
  }

  // Validate numeric parameters
  if (typeof start !== 'number' || typeof end !== 'number') {
    return res.status(400).json({
      error: 'Bad Request',
      message: 'start and end must be numbers'
    });
  }

  if (start >= end) {
    return res.status(400).json({
      error: 'Bad Request',
      message: 'start time must be less than end time'
    });
  }

  cutAndSaveVideo(folderName, filename, start, end, outputFolder)
    .then((outputFilename) => {
      res.json({
        success: true,
        message: 'Video cut successfully',
        data: {
          outputFilename,
          outputFolder,
          duration: end - start
        }
      });
    })
    .catch((error) => {
      console.error('Error cutting video:', error);
      res.status(500).json({
        error: 'Internal Server Error',
        message: (error as Error).message
      });
    });

  return;
});

// POST /api/video/convert - Convert video to different platform formats
router.post('/convert', (req: Request, res: Response) => {
  const { inputVideoPath, outputVideoPath, platform, newFileName } = req.body;

  // Validate required parameters
  if (!inputVideoPath || !outputVideoPath || !platform || !newFileName) {
    return res.status(400).json({
      error: 'Bad Request',
      message: 'Missing required parameters: inputVideoPath, outputVideoPath, platform, newFileName'
    });
  }

  // Validate platform parameter
  const validPlatforms: Platform[] = ['horizontal', 'vertical', 'original', 'square'];
  if (!validPlatforms.includes(platform)) {
    return res.status(400).json({
      error: 'Bad Request',
      message: `Invalid platform. Must be one of: ${validPlatforms.join(', ')}`
    });
  }

  convertVideo(inputVideoPath, outputVideoPath, platform, newFileName)
    .then((result) => {
      res.json({
        success: true,
        message: 'Video converted successfully',
        data: {
          outputFilename: result,
          platform,
          inputPath: inputVideoPath,
          outputPath: outputVideoPath
        }
      });
    })
    .catch((error) => {
      console.error('Error converting video:', error);
      res.status(500).json({
        error: 'Internal Server Error',
        message: (error as Error).message
      });
    });

  return;
});

// POST /api/video/convert-to-mp4 - Convert video to MP4 format
router.post('/convert-to-mp4', (req: Request, res: Response) => {
  const { inputPath, outputPath } = req.body;

  // Validate required parameters
  if (!inputPath || !outputPath) {
    return res.status(400).json({
      error: 'Bad Request',
      message: 'Missing required parameters: inputPath, outputPath'
    });
  }

  convertToMp4(inputPath, outputPath)
    .then((result) => {
      res.json({
        success: true,
        message: 'Video converted to MP4 successfully',
        data: {
          outputPath: result,
          inputPath
        }
      });
    })
    .catch((error) => {
      console.error('Error converting video to MP4:', error);
      res.status(500).json({
        error: 'Internal Server Error',
        message: (error as Error).message
      });
    });

  return;
});

// POST /api/video/upload-and-cut-download - Upload video, process, and download directly
router.post('/upload-and-cut-download', (req: Request, res: Response) => {
  const busboy = Busboy({ headers: req.headers });
  let uploadedFilePath = '';
  let formData: any = {};

  // Handle file upload
  busboy.on('file', (fieldname: string, file: NodeJS.ReadableStream, info: any): void => {
    const { filename, mimeType } = info;

    if (!filename || !mimeType.startsWith('video/')) {
      res.status(400).json({
        error: 'Bad Request',
        message: 'Please upload a valid video file'
      });
      return;
    }

    // Create unique filename
    const timestamp = new Date().getTime();
    const uniqueFilename = `${timestamp}_${filename}`;
    uploadedFilePath = path.join(process.cwd(), 'uploads', 'extrait-video', uniqueFilename);

    // Save uploaded file
    const writeStream = fs.createWriteStream(uploadedFilePath);
    file.pipe(writeStream);

    writeStream.on('error', (error) => {
      console.error('Error saving file:', error);
      res.status(500).json({
        error: 'Internal Server Error',
        message: 'Failed to save uploaded file'
      });
    });
  });

  // Handle form fields
  busboy.on('field', (fieldname: string, value: string) => {
    formData[fieldname] = value;
  });

  // Handle completion
  busboy.on('finish', async (): Promise<void> => {
    try {
      const { start, end, outputFolder, platform, paddingColor } = formData;

      // Validate required parameters
      if (!start || !end || !outputFolder) {
        res.status(400).json({
          error: 'Bad Request',
          message: 'Missing required parameters: start, end, outputFolder'
        });
        return;
      }

      const startTime = parseFloat(start);
      const endTime = parseFloat(end);

      if (isNaN(startTime) || isNaN(endTime) || startTime >= endTime) {
        res.status(400).json({
          error: 'Bad Request',
          message: 'Invalid start or end time'
        });
        return;
      }

      // Validate platform parameter if provided
      let platformType: Platform | undefined;
      if (platform) {
        const validPlatforms: Platform[] = ['horizontal', 'vertical', 'original', 'square'];
        if (!validPlatforms.includes(platform)) {
          res.status(400).json({
            error: 'Bad Request',
            message: `Invalid platform. Must be one of: ${validPlatforms.join(', ')}`
          });
          return;
        }
        platformType = platform;
      }

      // Convert color to FFmpeg-compatible format
      // FFmpeg only accepts named colors or hex codes in pad filter
      const convertToFFmpegColor = (color: string): string => {
        if (!color) return 'white';

        const namedColors = ['white', 'black', 'red', 'green', 'blue', 'yellow', 'gray', 'transparent'];
        const lowerColor = color.toLowerCase().trim();

        // Check if it's a valid named color
        if (namedColors.includes(lowerColor)) {
          return lowerColor;
        }

        // Check if it's a valid hex color code (#RRGGBB or #RGB)
        const hexColorRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/;
        if (hexColorRegex.test(color)) {
          return color;
        }

        // Convert RGB to hex
        const rgbRegex = /^rgba?\(\s*(\d{1,3})\s*,\s*(\d{1,3})\s*,\s*(\d{1,3})\s*(?:,\s*(0|1|0?\.\d+))?\s*\)$/i;
        const rgbMatch = color.match(rgbRegex);
        if (rgbMatch) {
          const [, r, g, b, a] = rgbMatch;
          const red = parseInt(r);
          const green = parseInt(g);
          const blue = parseInt(b);

          // Validate RGB values are in range 0-255
          if (red >= 0 && red <= 255 && green >= 0 && green <= 255 && blue >= 0 && blue <= 255) {
            // Convert to hex (ignore alpha for now as FFmpeg pad doesn't support it)
            const hex = `#${red.toString(16).padStart(2, '0')}${green.toString(16).padStart(2, '0')}${blue.toString(16).padStart(2, '0')}`;
            return hex.toUpperCase();
          }
        }

        // Convert HSL to hex
        const hslRegex = /^hsla?\(\s*(\d{1,3})\s*,\s*(\d{1,3})%\s*,\s*(\d{1,3})%\s*(?:,\s*(0|1|0?\.\d+))?\s*\)$/i;
        const hslMatch = color.match(hslRegex);
        if (hslMatch) {
          const [, h, s, l, a] = hslMatch;
          const hue = parseInt(h) / 360;
          const saturation = parseInt(s) / 100;
          const lightness = parseInt(l) / 100;

          // Validate HSL values are in range
          if (hue >= 0 && hue <= 1 && saturation >= 0 && saturation <= 1 && lightness >= 0 && lightness <= 1) {
            // Convert HSL to RGB
            const hslToRgb = (h: number, s: number, l: number) => {
              let r, g, b;

              if (s === 0) {
                r = g = b = l; // achromatic
              } else {
                const hue2rgb = (p: number, q: number, t: number) => {
                  if (t < 0) t += 1;
                  if (t > 1) t -= 1;
                  if (t < 1/6) return p + (q - p) * 6 * t;
                  if (t < 1/2) return q;
                  if (t < 2/3) return p + (q - p) * (2/3 - t) * 6;
                  return p;
                };

                const q = l < 0.5 ? l * (1 + s) : l + s - l * s;
                const p = 2 * l - q;
                r = hue2rgb(p, q, h + 1/3);
                g = hue2rgb(p, q, h);
                b = hue2rgb(p, q, h - 1/3);
              }

              return [Math.round(r * 255), Math.round(g * 255), Math.round(b * 255)];
            };

            const [red, green, blue] = hslToRgb(hue, saturation, lightness);
            const hex = `#${red.toString(16).padStart(2, '0')}${green.toString(16).padStart(2, '0')}${blue.toString(16).padStart(2, '0')}`;
            return hex.toUpperCase();
          }
        }

        // Default to white if invalid
        return 'white';
      };

      const selectedPaddingColor = convertToFFmpegColor(paddingColor);

      // Wait a bit for file to be fully written
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Process and stream video directly to user
      const filename = path.basename(uploadedFilePath);
      console.log(`Processing video: ${filename}, start: ${startTime}, end: ${endTime}, platform: ${platformType || 'none'}`);

      await processVideoStream(
        uploadedFilePath,
        startTime,
        endTime,
        platformType,
        selectedPaddingColor,
        res
      );

      // Clean up uploaded file after streaming
      try {
        fs.unlinkSync(uploadedFilePath);
        console.log('Cleaned up uploaded file:', uploadedFilePath);
      } catch (cleanupError) {
        console.error('Error cleaning up uploaded file:', cleanupError);
      }
    } catch (error) {
      console.error('Error processing video:', error);
      res.status(500).json({
        error: 'Internal Server Error',
        message: (error as Error).message
      });
    }
  });

  req.pipe(busboy);
});

// POST /api/video/upload-and-cut - Upload video, cut segment, and save to server
router.post('/upload-and-cut', (req: Request, res: Response) => {
  const busboy = Busboy({ headers: req.headers });
  let uploadedFilePath = '';
  let formData: any = {};

  // Handle file upload
  busboy.on('file', (_fieldname: string, file: NodeJS.ReadableStream, info: any): void => {
    const { filename, mimeType } = info;

    if (!filename || !mimeType.startsWith('video/')) {
      res.status(400).json({
        error: 'Bad Request',
        message: 'Please upload a valid video file'
      });
      return;
    }

    // Create unique filename
    const timestamp = new Date().getTime();
    const uniqueFilename = `${timestamp}_${filename}`;
    uploadedFilePath = path.join(process.cwd(), 'uploads', 'extrait-video', uniqueFilename);

    // Save uploaded file
    const writeStream = fs.createWriteStream(uploadedFilePath);
    file.pipe(writeStream);

    writeStream.on('error', (error) => {
      console.error('Error saving file:', error);
      res.status(500).json({
        error: 'Internal Server Error',
        message: 'Failed to save uploaded file'
      });
    });
  });

  // Handle form fields
  busboy.on('field', (fieldname: string, value: string) => {
    formData[fieldname] = value;
  });

  // Handle completion
  busboy.on('finish', async (): Promise<void> => {
    try {
      const { start, end, outputFolder, platform, paddingColor } = formData;

      // Validate required parameters
      if (!start || !end || !outputFolder) {
        res.status(400).json({
          error: 'Bad Request',
          message: 'Missing required parameters: start, end, outputFolder'
        });
        return;
      }

      const startTime = parseFloat(start);
      const endTime = parseFloat(end);

      if (isNaN(startTime) || isNaN(endTime) || startTime >= endTime) {
        res.status(400).json({
          error: 'Bad Request',
          message: 'Invalid start or end time'
        });
        return;
      }

      // Validate platform parameter if provided
      let platformType: Platform | undefined;
      if (platform) {
        const validPlatforms: Platform[] = ['horizontal', 'vertical', 'original', 'square'];
        if (!validPlatforms.includes(platform)) {
          res.status(400).json({
            error: 'Bad Request',
            message: `Invalid platform. Must be one of: ${validPlatforms.join(', ')}`
          });
          return;
        }
        platformType = platform;
      }

      // Convert color to FFmpeg-compatible format
      const convertToFFmpegColor = (color: string): string => {
        if (!color) return 'white';

        const namedColors = ['white', 'black', 'red', 'green', 'blue', 'yellow', 'gray', 'transparent'];
        const lowerColor = color.toLowerCase().trim();

        if (namedColors.includes(lowerColor)) {
          return lowerColor;
        }

        const hexColorRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/;
        if (hexColorRegex.test(color)) {
          return color;
        }

        // Convert RGB to hex
        const rgbRegex = /^rgba?\(\s*(\d{1,3})\s*,\s*(\d{1,3})\s*,\s*(\d{1,3})\s*(?:,\s*(0|1|0?\.\d+))?\s*\)$/i;
        const rgbMatch = color.match(rgbRegex);
        if (rgbMatch) {
          const [, r, g, b] = rgbMatch;
          const red = parseInt(r);
          const green = parseInt(g);
          const blue = parseInt(b);

          if (red >= 0 && red <= 255 && green >= 0 && green <= 255 && blue >= 0 && blue <= 255) {
            const hex = `#${red.toString(16).padStart(2, '0')}${green.toString(16).padStart(2, '0')}${blue.toString(16).padStart(2, '0')}`;
            return hex.toUpperCase();
          }
        }

        // Convert HSL to hex
        const hslRegex = /^hsla?\(\s*(\d{1,3})\s*,\s*(\d{1,3})%\s*,\s*(\d{1,3})%\s*(?:,\s*(0|1|0?\.\d+))?\s*\)$/i;
        const hslMatch = color.match(hslRegex);
        if (hslMatch) {
          const [, h, s, l] = hslMatch;
          const hue = parseInt(h) / 360;
          const saturation = parseInt(s) / 100;
          const lightness = parseInt(l) / 100;

          if (hue >= 0 && hue <= 1 && saturation >= 0 && saturation <= 1 && lightness >= 0 && lightness <= 1) {
            const hslToRgb = (h: number, s: number, l: number) => {
              let r, g, b;

              if (s === 0) {
                r = g = b = l;
              } else {
                const hue2rgb = (p: number, q: number, t: number) => {
                  if (t < 0) t += 1;
                  if (t > 1) t -= 1;
                  if (t < 1/6) return p + (q - p) * 6 * t;
                  if (t < 1/2) return q;
                  if (t < 2/3) return p + (q - p) * (2/3 - t) * 6;
                  return p;
                };

                const q = l < 0.5 ? l * (1 + s) : l + s - l * s;
                const p = 2 * l - q;
                r = hue2rgb(p, q, h + 1/3);
                g = hue2rgb(p, q, h);
                b = hue2rgb(p, q, h - 1/3);
              }

              return [Math.round(r * 255), Math.round(g * 255), Math.round(b * 255)];
            };

            const [red, green, blue] = hslToRgb(hue, saturation, lightness);
            const hex = `#${red.toString(16).padStart(2, '0')}${green.toString(16).padStart(2, '0')}${blue.toString(16).padStart(2, '0')}`;
            return hex.toUpperCase();
          }
        }

        return 'white';
      };

      const selectedPaddingColor = convertToFFmpegColor(paddingColor);

      // Wait a bit for file to be fully written
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Process video cutting and save to server
      const filename = path.basename(uploadedFilePath);
      const cutOutputFilename = await cutAndSaveVideo('extrait-video', filename, startTime, endTime, outputFolder);

      let finalOutputFilename = cutOutputFilename;
      let conversionResult = null;

      // If platform is specified, also convert the cut video
      if (platformType) {
        try {
          const timestamp = new Date().getTime();
          const cutFilePath = path.join(process.cwd(), 'uploads', outputFolder, cutOutputFilename);

          const fileExtension = path.extname(cutOutputFilename);
          const convertedFilename = `${timestamp}_${platformType}_converted${fileExtension}`;

          const tempInputFilename = `temp_${cutOutputFilename}`;
          const tempInputPath = path.join(process.cwd(), 'uploads', 'extrait-video', tempInputFilename);
          fs.copyFileSync(cutFilePath, tempInputPath);

          const convertResult = await convertVideo(
            tempInputFilename,
            convertedFilename,
            platformType,
            convertedFilename,
            selectedPaddingColor
          );

          fs.unlinkSync(tempInputPath);

          finalOutputFilename = convertResult;
          conversionResult = {
            platform: platformType,
            convertedFilename: convertResult,
            originalCutFile: cutOutputFilename
          };
        } catch (conversionError) {
          console.error('Error converting cut video:', conversionError);
        }
      }

      res.json({
        success: true,
        message: platformType
          ? 'Video uploaded, cut, and converted successfully. Files saved on server.'
          : 'Video uploaded and cut successfully. Files saved on server.',
        data: {
          originalFilename: filename,
          cutFilename: cutOutputFilename,
          finalOutputFilename,
          outputFolder,
          duration: endTime - startTime,
          uploadedFile: uploadedFilePath,
          platform: platformType || null,
          conversion: conversionResult
        }
      });
    } catch (error) {
      console.error('Error processing video:', error);
      res.status(500).json({
        error: 'Internal Server Error',
        message: (error as Error).message
      });
    }
  });

  req.pipe(busboy);
});

// POST /api/video/upload-and-convert - Upload video and convert to platform format
router.post('/upload-and-convert', (req: Request, res: Response) => {
  const busboy = Busboy({ headers: req.headers });
  let uploadedFilePath = '';
  let formData: any = {};

  // Handle file upload
  busboy.on('file', (_fieldname: string, file: NodeJS.ReadableStream, info: any): void => {
    const { filename, mimeType } = info;

    if (!filename || !mimeType.startsWith('video/')) {
      res.status(400).json({
        error: 'Bad Request',
        message: 'Please upload a valid video file'
      });
      return;
    }

    // Create unique filename
    const timestamp = new Date().getTime();
    const uniqueFilename = `${timestamp}_${filename}`;
    uploadedFilePath = path.join(process.cwd(), 'uploads', 'extrait-video', uniqueFilename);

    // Save uploaded file
    const writeStream = fs.createWriteStream(uploadedFilePath);
    file.pipe(writeStream);

    writeStream.on('error', (error) => {
      console.error('Error saving file:', error);
      res.status(500).json({
        error: 'Internal Server Error',
        message: 'Failed to save uploaded file'
      });
    });
  });

  // Handle form fields
  busboy.on('field', (fieldname: string, value: string) => {
    formData[fieldname] = value;
  });

  // Handle completion
  busboy.on('finish', async (): Promise<void> => {
    try {
      const { platform, paddingColor } = formData;

      // Validate required parameters
      if (!platform) {
        res.status(400).json({
          error: 'Bad Request',
          message: 'Missing required parameter: platform'
        });
        return;
      }

      // Validate platform parameter
      const validPlatforms: Platform[] = ['horizontal', 'vertical', 'original', 'square'];
      if (!validPlatforms.includes(platform)) {
        res.status(400).json({
          error: 'Bad Request',
          message: `Invalid platform. Must be one of: ${validPlatforms.join(', ')}`
        });
        return;
      }

      // Convert color to FFmpeg-compatible format
      // FFmpeg only accepts named colors or hex codes in pad filter
      const convertToFFmpegColor = (color: string): string => {
        if (!color) return 'white';

        const namedColors = ['white', 'black', 'red', 'green', 'blue', 'yellow', 'gray', 'transparent'];
        const lowerColor = color.toLowerCase().trim();

        // Check if it's a valid named color
        if (namedColors.includes(lowerColor)) {
          return lowerColor;
        }

        // Check if it's a valid hex color code (#RRGGBB or #RGB)
        const hexColorRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/;
        if (hexColorRegex.test(color)) {
          return color;
        }

        // Convert RGB to hex
        const rgbRegex = /^rgba?\(\s*(\d{1,3})\s*,\s*(\d{1,3})\s*,\s*(\d{1,3})\s*(?:,\s*(0|1|0?\.\d+))?\s*\)$/i;
        const rgbMatch = color.match(rgbRegex);
        if (rgbMatch) {
          const [, r, g, b] = rgbMatch;
          const red = parseInt(r);
          const green = parseInt(g);
          const blue = parseInt(b);

          // Validate RGB values are in range 0-255
          if (red >= 0 && red <= 255 && green >= 0 && green <= 255 && blue >= 0 && blue <= 255) {
            // Convert to hex (ignore alpha for now as FFmpeg pad doesn't support it)
            const hex = `#${red.toString(16).padStart(2, '0')}${green.toString(16).padStart(2, '0')}${blue.toString(16).padStart(2, '0')}`;
            return hex.toUpperCase();
          }
        }

        // Convert HSL to hex
        const hslRegex = /^hsla?\(\s*(\d{1,3})\s*,\s*(\d{1,3})%\s*,\s*(\d{1,3})%\s*(?:,\s*(0|1|0?\.\d+))?\s*\)$/i;
        const hslMatch = color.match(hslRegex);
        if (hslMatch) {
          const [, h, s, l] = hslMatch;
          const hue = parseInt(h) / 360;
          const saturation = parseInt(s) / 100;
          const lightness = parseInt(l) / 100;

          // Validate HSL values are in range
          if (hue >= 0 && hue <= 1 && saturation >= 0 && saturation <= 1 && lightness >= 0 && lightness <= 1) {
            // Convert HSL to RGB
            const hslToRgb = (h: number, s: number, l: number) => {
              let r, g, b;

              if (s === 0) {
                r = g = b = l; // achromatic
              } else {
                const hue2rgb = (p: number, q: number, t: number) => {
                  if (t < 0) t += 1;
                  if (t > 1) t -= 1;
                  if (t < 1/6) return p + (q - p) * 6 * t;
                  if (t < 1/2) return q;
                  if (t < 2/3) return p + (q - p) * (2/3 - t) * 6;
                  return p;
                };

                const q = l < 0.5 ? l * (1 + s) : l + s - l * s;
                const p = 2 * l - q;
                r = hue2rgb(p, q, h + 1/3);
                g = hue2rgb(p, q, h);
                b = hue2rgb(p, q, h - 1/3);
              }

              return [Math.round(r * 255), Math.round(g * 255), Math.round(b * 255)];
            };

            const [red, green, blue] = hslToRgb(hue, saturation, lightness);
            const hex = `#${red.toString(16).padStart(2, '0')}${green.toString(16).padStart(2, '0')}${blue.toString(16).padStart(2, '0')}`;
            return hex.toUpperCase();
          }
        }

        // Default to white if invalid
        return 'white';
      };

      const selectedPaddingColor = convertToFFmpegColor(paddingColor);

      // Wait a bit for file to be fully written
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Process video conversion
      const inputFilename = path.basename(uploadedFilePath);
      const timestamp = new Date().getTime();
      const outputFilename = `${timestamp}_converted_${platform}${path.extname(inputFilename)}`;

      const result = await convertVideo(inputFilename, outputFilename, platform, outputFilename, selectedPaddingColor);

      res.json({
        success: true,
        message: 'Video uploaded and converted successfully',
        data: {
          originalFilename: inputFilename,
          outputFilename: result,
          platform,
          uploadedFile: uploadedFilePath
        }
      });
    } catch (error) {
      console.error('Error processing video:', error);
      res.status(500).json({
        error: 'Internal Server Error',
        message: (error as Error).message
      });
    }
  });

  req.pipe(busboy);
});

// GET /api/video - Get video processing endpoints info
router.get('/', (_req: Request, res: Response) => {
  res.json({
    message: 'Video Processing API',
    version: '1.0.0',
    endpoints: [
      'GET /api/video',
      'POST /api/video/cut',
      'POST /api/video/convert',
      'POST /api/video/convert-to-mp4',
      'POST /api/video/upload-and-cut',
      'POST /api/video/upload-and-cut-download',
      'POST /api/video/upload-and-convert'
    ],
    supportedPlatforms: ['horizontal', 'vertical', 'original', 'square'],
    platformDescriptions: {
      horizontal: '1920x1080 (YouTube) - Entire video visible with customizable padding',
      vertical: '1080x1920 (TikTok/Stories) - Entire video visible with customizable padding',
      square: '1080x1080 (Instagram) - Entire video visible with customizable padding',
      original: 'Original dimensions - No changes to resolution or aspect ratio'
    },
    uploadEndpoints: {
      'upload-and-cut': 'Upload video file, cut segment, and save to server (traditional)',
      'upload-and-cut-download': 'Upload video file, process, and download directly (no server storage)',
      'upload-and-convert': 'Upload video file and convert to platform format'
    },
    uploadAndCutParameters: {
      required: ['video (file)', 'start (number)', 'end (number)', 'outputFolder (string)'],
      optional: [
        'platform (string): horizontal|vertical|original|square',
        'paddingColor (string): Named colors, hex codes, RGB, or HSL - default: white'
      ]
    },
    uploadAndConvertParameters: {
      required: ['video (file)', 'platform (string)'],
      optional: ['paddingColor (string): Named colors, hex codes, RGB, or HSL - default: white']
    },
    paddingColorFormats: {
      namedColors: ['white', 'black', 'red', 'green', 'blue', 'yellow', 'gray', 'transparent'],
      hexCodes: ['#FF0000', '#00FF00', '#0000FF', '#FFA500'],
      rgbColors: ['rgb(255,0,0)', 'rgb(0,255,0)', 'rgba(255,0,0,0.5)'],
      hslColors: ['hsl(0,100%,50%)', 'hsl(120,100%,50%)', 'hsla(240,100%,50%,0.8)']
    },
    colorExamples: {
      red: {
        named: 'red',
        hex: '#FF0000',
        rgb: 'rgb(255,0,0)',
        hsl: 'hsl(0,100%,50%)'
      },
      blue: {
        named: 'blue',
        hex: '#0000FF',
        rgb: 'rgb(0,0,255)',
        hsl: 'hsl(240,100%,50%)'
      },
      orange: {
        hex: '#FFA500',
        rgb: 'rgb(255,165,0)',
        hsl: 'hsl(39,100%,50%)'
      }
    }
  });
});

export default router;
