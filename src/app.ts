import express, { Application, Request, Response, NextFunction } from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Import routes
import apiRoutes from './routes/api';
import videoRoutes from './routes/video';

// Import Swagger
import { swaggerUi, specs } from './config/swagger';

// Create Express application
const app: Application = express();

// Middleware
app.use(helmet()); // Security headers
app.use(cors()); // Enable CORS
app.use(morgan('combined')); // Logging
app.use(express.json({ limit: '10mb' })); // Parse JSON bodies
app.use(express.urlencoded({ extended: true })); // Parse URL-encoded bodies

// Swagger Documentation
app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(specs, {
  explorer: true,
  customCss: '.swagger-ui .topbar { display: none }',
  customSiteTitle: 'Video Processing API Documentation'
}));

// Routes
app.use('/api', apiRoutes);
app.use('/api/video', videoRoutes);

// Health check endpoint
app.get('/health', (_req: Request, res: Response) => {
  res.status(200).json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    uptime: process.uptime()
  });
});

// Root endpoint
app.get('/', (_req: Request, res: Response) => {
  res.json({
    message: 'Video Processing API Server',
    version: '2.1.0',
    description: 'Upload, process, and download videos directly without server storage',
    endpoints: {
      health: '/health',
      api: '/api',
      videoProcessing: '/api/video/upload-and-cut-download',
      apiInfo: '/api/video',
      documentation: '/api-docs'
    },
    documentation: {
      swagger: '/api-docs',
      description: 'Interactive API documentation with testing capabilities'
    }
  });
});

// 404 handler
app.use('*', (req: Request, res: Response) => {
  res.status(404).json({
    error: 'Not Found',
    message: `Route ${req.originalUrl} not found`
  });
});

// Global error handler
app.use((error: Error, _req: Request, res: Response, _next: NextFunction) => {
  console.error('Error:', error.message);
  console.error('Stack:', error.stack);
  
  res.status(500).json({
    error: 'Internal Server Error',
    message: process.env.NODE_ENV === 'development' ? error.message : 'Something went wrong'
  });
});

export default app;

