import ffmpeg from 'fluent-ffmpeg';
import { path as ffmpegPath } from '@ffmpeg-installer/ffmpeg';
import { path as ffprobePath } from '@ffprobe-installer/ffprobe';
import getVideoInfos from 'get-video-info';
import * as path from 'path';
import * as fs from 'fs';
import { 
  VideoMetadata, 
  VideoInfo, 
  VideoCutParams, 
  VideoConvertParams, 
  VideoConvertToMp4Params,
  Platform 
} from '../types/video';

// Configure FFmpeg paths
ffmpeg.setFfmpegPath(ffmpegPath);
ffmpeg.setFfprobePath(ffprobePath);

/**
 * Cut and save a video segment
 */
export async function cutAndSaveVideo(
  folderName: string,
  filename: string,
  start: number,
  end: number,
  outputFolder: string
): Promise<string> {
  try {
    console.log('Output folder:', outputFolder);
    const outputFolderPath = `../../uploads/${outputFolder}`;

    // Create output directory if it doesn't exist
    if (!fs.existsSync(outputFolderPath)) {
      fs.mkdirSync(outputFolderPath, { recursive: true });
    }

    const outputFilename = new Date().getTime() + path.extname(filename);
    const outputPath = path.join(__dirname, outputFolderPath, outputFilename);
    const filePath = path.join(__dirname, '../../uploads', folderName, filename);

    // Check if input file exists
    if (!fs.existsSync(filePath)) {
      throw new Error('File not found');
    }

    // Get video information
    const fileInfo: VideoInfo = await getVideoInfos(filePath);
    const durationInSeconds = fileInfo.format.duration;
    const duration = end - start;

    // Validate time parameters
    if (start < 0 || start > durationInSeconds || end > durationInSeconds || duration <= 0) {
      throw new Error('Invalid start time or end time');
    }

    // Process video cutting
    await new Promise<void>((resolve, reject) => {
      ffmpeg(path.join(__dirname, '../../uploads', folderName, filename))
        .setStartTime(start)
        .setDuration(duration)
        .output(outputPath)
        .videoCodec('copy') // Copy video stream without re-encoding
        .audioCodec('copy') // Copy audio stream without re-encoding
        .addOption('-avoid_negative_ts', '1') // Avoid negative timestamps
        .addOption('-movflags', 'faststart') // Optimize MP4 for streaming
        .on('start', (commandLine: string) => {
          console.log('Spawned Ffmpeg with command: ' + commandLine);
          console.log('Processing');
        })
        .on('end', () => {
          console.log('Conversion Done');
          resolve();
        })
        .on('error', (err: Error) => {
          console.log('error: ', err);
          reject(err);
        })
        .run();
    });

    return outputFilename;
  } catch (error) {
    throw new Error((error as Error).message);
  }
}

/**
 * Get video dimensions using ffprobe
 */
export function getVideoDimensions(videoPath: string): Promise<VideoMetadata> {
  return new Promise((resolve, reject) => {
    ffmpeg.ffprobe(videoPath, (err: any, metadata: any) => {
      if (err) {
        console.log('Error getting video dimensions:', err);
        reject(err);
      } else {
        console.log('Metadata:', metadata.streams[0].width, metadata.streams[0].height);
        const mediaWidth = metadata.streams[0].width;
        const mediaHeight = metadata.streams[0].height;
        resolve({ mediaWidth, mediaHeight });
      }
    });
  });
}

/**
 * Convert video to different formats/platforms
 */
export async function convertVideo(
  inputVideoPath: string,
  outputVideoPath: string,
  platform: Platform,
  newFileName: string
): Promise<string> {
  console.log('Converting video...');
  console.log('Input video path:', inputVideoPath);
  console.log('Output video path:', outputVideoPath);
  console.log('Platform:', platform);

  const fullInputPath = path.join(__dirname, '../../uploads/extrait-video', inputVideoPath);
  const fullOutputPath = path.join(__dirname, '../../uploads/extrait-video', outputVideoPath);
  console.log('Full output video path:', fullOutputPath);

  return new Promise(async (resolve, reject) => {
    try {
      const { mediaWidth, mediaHeight } = await getVideoDimensions(fullInputPath);
      console.log('Video dimensions:', mediaWidth, mediaHeight);

      if (platform === 'horizontal') {
        await processHorizontalVideo(fullInputPath, fullOutputPath, mediaWidth, mediaHeight, newFileName, resolve, reject);
      } else if (platform === 'vertical') {
        await processVerticalVideo(fullInputPath, fullOutputPath, mediaWidth, mediaHeight, newFileName, resolve, reject);
      } else if (platform === 'original') {
        await processOriginalVideo(fullInputPath, fullOutputPath, mediaWidth, mediaHeight, newFileName, resolve, reject);
      } else if (platform === 'square') {
        await processSquareVideo(fullInputPath, fullOutputPath, mediaWidth, mediaHeight, newFileName, resolve, reject);
      } else {
        reject(new Error('Video dimensions not detected!'));
      }
    } catch (err) {
      console.error('Error:', err);
      reject(err);
    }
  });
}

/**
 * Process horizontal video format
 */
async function processHorizontalVideo(
  inputPath: string,
  outputPath: string,
  mediaWidth: number,
  mediaHeight: number,
  newFileName: string,
  resolve: (value: string) => void,
  reject: (reason?: any) => void
): Promise<void> {
  if (mediaWidth && mediaHeight) {
    console.log('Processing horizontal video format');

    ffmpeg(inputPath)
      .output(outputPath)
      .videoCodec('copy') // Copy video stream without re-encoding
      .audioCodec('copy') // Copy audio stream without re-encoding
      .addOption('-avoid_negative_ts', '1') // Avoid negative timestamps
      .addOption('-movflags', 'faststart') // Optimize MP4 for streaming
      .on('start', (commandLine: string) => {
        console.log('Spawned FFmpeg with command:', commandLine);
        console.log('Converting video...');
      })
      .on('end', () => {
        console.log('Video conversion complete!');
        resolve(newFileName);
      })
      .on('error', (err: Error) => {
        console.error('Error converting video:', err);
        reject(err);
      })
      .run();
  }
}

/**
 * Process vertical video format
 */
async function processVerticalVideo(
  inputPath: string,
  outputPath: string,
  mediaWidth: number,
  mediaHeight: number,
  newFileName: string,
  resolve: (value: string) => void,
  reject: (reason?: any) => void
): Promise<void> {
  console.log('Vertical video processing...', 'vertical');
  if (mediaWidth && mediaHeight) {
    console.log('Square video processing...', mediaWidth, mediaHeight);
    const desiredSize = Math.min(mediaWidth, mediaHeight); // Choose the smaller dimension

    const cropWidth = desiredSize;
    const cropHeight = desiredSize;
    const cropFromX = Math.floor((mediaWidth - cropWidth) / 2);
    const cropFromY = Math.floor((mediaHeight - cropHeight) / 2);

    ffmpeg(inputPath)
      .output(outputPath)
      .videoFilter([
        {
          filter: 'crop',
          options: {
            w: cropWidth,
            h: cropHeight,
            x: cropFromX,
            y: cropFromY,
          },
        },
        {
          filter: 'eq',
          options: 'brightness=0.1:saturation=2',
        },
      ])
      .videoCodec('libx264') // Encode video with libx264 codec
      .audioCodec('copy') // Copy audio stream without re-encoding
      .addOption('-avoid_negative_ts', '1') // Avoid negative timestamps
      .addOption('-movflags', 'faststart') // Optimize MP4 for streaming
      .on('start', (commandLine: string) => {
        console.log('Spawned FFmpeg with command:', commandLine);
        console.log('Converting video...');
      })
      .on('end', () => {
        console.log('Video conversion complete!');
        resolve(newFileName);
      })
      .on('error', (err: Error) => {
        console.error('Error converting video:', err);
        reject(err);
      })
      .run();
  }
}

/**
 * Process original video format (copy without changes)
 */
async function processOriginalVideo(
  inputPath: string,
  outputPath: string,
  mediaWidth: number,
  mediaHeight: number,
  newFileName: string,
  resolve: (value: string) => void,
  reject: (reason?: any) => void
): Promise<void> {
  if (mediaWidth && mediaHeight) {
    console.log('Original video processing...');

    ffmpeg(inputPath)
      .output(outputPath)
      .videoCodec('copy') // Copy video stream without re-encoding
      .audioCodec('copy') // Copy audio stream without re-encoding
      .addOption('-avoid_negative_ts', '1') // Avoid negative timestamps
      .addOption('-movflags', 'faststart') // Optimize MP4 for streaming
      .on('start', (commandLine: string) => {
        console.log('Spawned FFmpeg with command:', commandLine);
        console.log('Copying original video...');
      })
      .on('end', () => {
        console.log('Video conversion complete!');
        resolve(newFileName);
      })
      .on('error', (err: Error) => {
        console.error('Error copying original video:', err);
        reject(err);
      })
      .run();
  } else {
    console.log('Video dimensions not detected!');
    reject(new Error('Video dimensions not detected!'));
  }
}

/**
 * Process square video format
 */
async function processSquareVideo(
  inputPath: string,
  outputPath: string,
  mediaWidth: number,
  mediaHeight: number,
  newFileName: string,
  resolve: (value: string) => void,
  reject: (reason?: any) => void
): Promise<void> {
  if (mediaWidth && mediaHeight) {
    const desiredWidth = 1080;
    const desiredHeight = 1080;
    const desiredAspectRatio = desiredWidth / desiredHeight;

    // Calculate the dimensions to maintain the input aspect ratio
    const inputAspectRatio = mediaWidth / mediaHeight;
    let newWidth: number, newHeight: number, paddingX: number, paddingY: number;

    if (inputAspectRatio > desiredAspectRatio) {
      // Landscape video
      newWidth = desiredWidth;
      newHeight = Math.round(desiredWidth / inputAspectRatio);
      paddingY = Math.floor((desiredHeight - newHeight) / 2);
      paddingX = 0;
    } else {
      // Portrait video
      newWidth = Math.round(desiredHeight * inputAspectRatio);
      newHeight = desiredHeight;
      paddingX = Math.floor((desiredWidth - newWidth) / 2);
      paddingY = 0;
    }

    ffmpeg(inputPath)
      .output(outputPath)
      .videoFilters([
        `scale=${newWidth}:${newHeight}`, // Scale to maintain aspect ratio
        `pad=${desiredWidth}:${desiredHeight}:${paddingX}:${paddingY}:white`, // Add padding to fit square format
      ])
      .audioCodec('copy')
      .addOption('-avoid_negative_ts', '1')
      .addOption('-movflags', 'faststart')
      .addOption('-preset', 'fast') // Faster encoding preset
      .addOption('-profile:v', 'main') // Main profile for compatibility
      .addOption('-crf', '23') // Constant Rate Factor for quality
      .on('start', (commandLine: string) => {
        console.log('Spawned FFmpeg with command:', commandLine);
        console.log('Converting video...');
      })
      .on('end', () => {
        console.log('Video conversion complete!');
        resolve(newFileName);
      })
      .on('error', (err: Error) => {
        console.error('Error converting video:', err);
        reject(err);
      })
      .run();
  }
}

/**
 * Convert video to MP4 format
 */
export function convertToMp4(inputPath: string, outputPath: string): Promise<string> {
  return new Promise((resolve, reject) => {
    ffmpeg(inputPath)
      .output(outputPath)
      .videoCodec('libx264') // Use H.264 codec
      .audioCodec('aac') // Use AAC codec for audio
      .outputOptions([
        '-preset fast', // Faster encoding preset
        '-crf 23', // Constant rate factor for quality control
        '-movflags +faststart' // Enables fast start for MP4
      ])
      .on('end', () => {
        console.log('Conversion finished');
        resolve(outputPath);
      })
      .on('error', (err: Error) => {
        console.error('Conversion error: ', err);
        reject(err);
      })
      .run();
  });
}
