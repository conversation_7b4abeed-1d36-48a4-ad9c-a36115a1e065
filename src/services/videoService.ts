import ffmpeg from 'fluent-ffmpeg';
import * as path from 'path';
import * as fs from 'fs';
import {
  VideoMetadata,
  VideoInfo,
  VideoCutParams,
  VideoConvertParams,
  VideoConvertToMp4Params,
  Platform
} from '../types/video';

// Configure FFmpeg to use system installation
try {
  const { execSync } = require('child_process');
  const systemFfmpeg = execSync('which ffmpeg', { encoding: 'utf8' }).trim();
  const systemFfprobe = execSync('which ffprobe', { encoding: 'utf8' }).trim();
  if (systemFfmpeg) ffmpeg.setFfmpegPath(systemFfmpeg);
  if (systemFfprobe) ffmpeg.setFfprobePath(systemFfprobe);
  console.log('Using system FFmpeg:', systemFfmpeg);
  console.log('Using system FFprobe:', systemFfprobe);
} catch (error) {
  console.warn('No FFmpeg found. Please install FFmpeg manually or via package manager.');
}

/**
 * Get video information using ffprobe
 */
function getVideoInfo(filePath: string): Promise<VideoInfo> {
  return new Promise((resolve, reject) => {
    ffmpeg.ffprobe(filePath, (err: any, metadata: any) => {
      if (err) {
        console.log('Error getting video info:', err);
        reject(err);
      } else {
        resolve({
          format: {
            duration: metadata.format.duration
          }
        });
      }
    });
  });
}

/**
 * Cut and save a video segment
 */
export async function cutAndSaveVideo(
  folderName: string,
  filename: string,
  start: number,
  end: number,
  outputFolder: string
): Promise<string> {
  try {
    console.log('Output folder:', outputFolder);
    const outputFolderPath = path.join(process.cwd(), 'uploads', outputFolder);

    // Create output directory if it doesn't exist
    if (!fs.existsSync(outputFolderPath)) {
      fs.mkdirSync(outputFolderPath, { recursive: true });
    }

    const outputFilename = new Date().getTime() + path.extname(filename);
    const outputPath = path.join(outputFolderPath, outputFilename);
    const filePath = path.join(process.cwd(), 'uploads', folderName, filename);

    // Check if input file exists
    if (!fs.existsSync(filePath)) {
      throw new Error('File not found');
    }

    // Get video information using ffprobe
    const fileInfo: VideoInfo = await getVideoInfo(filePath);
    const durationInSeconds = fileInfo.format.duration;
    const duration = end - start;

    // Validate time parameters
    if (start < 0 || start > durationInSeconds || end > durationInSeconds || duration <= 0) {
      throw new Error('Invalid start time or end time');
    }

    // Process video cutting
    await new Promise<void>((resolve, reject) => {
      ffmpeg(filePath)
        .setStartTime(start)
        .setDuration(duration)
        .output(outputPath)
        .videoCodec('copy') // Copy video stream without re-encoding
        .audioCodec('copy') // Copy audio stream without re-encoding
        .addOption('-avoid_negative_ts', '1') // Avoid negative timestamps
        .addOption('-movflags', 'faststart') // Optimize MP4 for streaming
        .on('start', (commandLine: string) => {
          console.log('Spawned Ffmpeg with command: ' + commandLine);
          console.log('Processing');
        })
        .on('end', () => {
          console.log('Conversion Done');
          resolve();
        })
        .on('error', (err: Error) => {
          console.log('error: ', err);
          reject(err);
        })
        .run();
    });

    return outputFilename;
  } catch (error) {
    throw new Error((error as Error).message);
  }
}

/**
 * Get video dimensions using ffprobe
 */
export function getVideoDimensions(videoPath: string): Promise<VideoMetadata> {
  return new Promise((resolve, reject) => {
    ffmpeg.ffprobe(videoPath, (err: any, metadata: any) => {
      if (err) {
        console.log('Error getting video dimensions:', err);
        reject(err);
      } else {
        console.log('Metadata:', metadata.streams[0].width, metadata.streams[0].height);
        const mediaWidth = metadata.streams[0].width;
        const mediaHeight = metadata.streams[0].height;
        resolve({ mediaWidth, mediaHeight });
      }
    });
  });
}

/**
 * Convert video to different formats/platforms
 */
export async function convertVideo(
  inputVideoPath: string,
  outputVideoPath: string,
  platform: Platform,
  newFileName: string,
  paddingColor: string = 'white'
): Promise<string> {
  console.log('Converting video...');
  console.log('Input video path:', inputVideoPath);
  console.log('Output video path:', outputVideoPath);
  console.log('Platform:', platform);

  const fullInputPath = path.join(process.cwd(), 'uploads', 'extrait-video', inputVideoPath);
  const fullOutputPath = path.join(process.cwd(), 'uploads', 'extrait-video', outputVideoPath);
  console.log('Full output video path:', fullOutputPath);

  return new Promise(async (resolve, reject) => {
    try {
      const { mediaWidth, mediaHeight } = await getVideoDimensions(fullInputPath);
      console.log('Video dimensions:', mediaWidth, mediaHeight);

      if (platform === 'horizontal') {
        await processHorizontalVideo(fullInputPath, fullOutputPath, mediaWidth, mediaHeight, newFileName, paddingColor, resolve, reject);
      } else if (platform === 'vertical') {
        await processVerticalVideo(fullInputPath, fullOutputPath, mediaWidth, mediaHeight, newFileName, paddingColor, resolve, reject);
      } else if (platform === 'original') {
        await processOriginalVideo(fullInputPath, fullOutputPath, mediaWidth, mediaHeight, newFileName, resolve, reject);
      } else if (platform === 'square') {
        await processSquareVideo(fullInputPath, fullOutputPath, mediaWidth, mediaHeight, newFileName, paddingColor, resolve, reject);
      } else {
        reject(new Error('Video dimensions not detected!'));
      }
    } catch (err) {
      console.error('Error:', err);
      reject(err);
    }
  });
}

/**
 * Process horizontal video format (1920x1080 - YouTube optimized)
 * Ensures entire video is visible with black padding if needed
 */
async function processHorizontalVideo(
  inputPath: string,
  outputPath: string,
  mediaWidth: number,
  mediaHeight: number,
  newFileName: string,
  paddingColor: string,
  resolve: (value: string) => void,
  reject: (reason?: any) => void
): Promise<void> {
  if (mediaWidth && mediaHeight) {
    console.log('Processing horizontal video format (1920x1080)');

    const desiredWidth = 1920;
    const desiredHeight = 1080;
    const desiredAspectRatio = desiredWidth / desiredHeight;
    const inputAspectRatio = mediaWidth / mediaHeight;

    let newWidth: number, newHeight: number, paddingX: number, paddingY: number;

    if (inputAspectRatio > desiredAspectRatio) {
      // Input is wider than 16:9 - fit to width and add vertical padding
      newWidth = desiredWidth;
      newHeight = Math.round(desiredWidth / inputAspectRatio);
      paddingX = 0;
      paddingY = Math.floor((desiredHeight - newHeight) / 2);
    } else {
      // Input is taller than 16:9 - fit to height and add horizontal padding
      newHeight = desiredHeight;
      newWidth = Math.round(desiredHeight * inputAspectRatio);
      paddingY = 0;
      paddingX = Math.floor((desiredWidth - newWidth) / 2);
    }

    // Validate padding color for FFmpeg compatibility
    const validFFmpegColor = paddingColor === 'transparent' ? 'black@0' : paddingColor;
    console.log(`Horizontal padding color: ${validFFmpegColor}`);

    ffmpeg(inputPath)
      .output(outputPath)
      .videoFilters([
        `scale=${newWidth}:${newHeight}`, // Scale to fit while maintaining aspect ratio
        `pad=${desiredWidth}:${desiredHeight}:${paddingX}:${paddingY}:${validFFmpegColor}`, // Add padding with chosen color
      ])
      .audioCodec('copy')
      .addOption('-avoid_negative_ts', '1')
      .addOption('-movflags', 'faststart')
      .addOption('-preset', 'fast')
      .addOption('-profile:v', 'main')
      .addOption('-crf', '23')
      .on('start', (commandLine: string) => {
        console.log('Spawned FFmpeg with command:', commandLine);
        console.log('Converting to horizontal format...');
      })
      .on('end', () => {
        console.log('Horizontal video conversion complete!');
        resolve(newFileName);
      })
      .on('error', (err: Error) => {
        console.error('Error converting horizontal video:', err);
        reject(err);
      })
      .run();
  }
}

/**
 * Process vertical video format (1080x1920 - TikTok/Instagram Stories optimized)
 * Ensures entire video is visible with black padding if needed
 */
async function processVerticalVideo(
  inputPath: string,
  outputPath: string,
  mediaWidth: number,
  mediaHeight: number,
  newFileName: string,
  paddingColor: string,
  resolve: (value: string) => void,
  reject: (reason?: any) => void
): Promise<void> {
  console.log('Processing vertical video format (1080x1920)');
  if (mediaWidth && mediaHeight) {
    const desiredWidth = 1080;
    const desiredHeight = 1920;
    const desiredAspectRatio = desiredWidth / desiredHeight; // 0.5625 (9:16)
    const inputAspectRatio = mediaWidth / mediaHeight;

    console.log(`Input: ${mediaWidth}x${mediaHeight}, aspect ratio: ${inputAspectRatio}`);
    console.log(`Target: ${desiredWidth}x${desiredHeight}, aspect ratio: ${desiredAspectRatio}`);

    let newWidth: number, newHeight: number, paddingX: number, paddingY: number;

    if (inputAspectRatio > desiredAspectRatio) {
      // Input is wider than 9:16 - scale to fit height, add horizontal padding
      newHeight = desiredHeight;
      newWidth = Math.round(desiredHeight * inputAspectRatio);
      paddingY = 0;
      paddingX = Math.floor((desiredWidth - newWidth) / 2);

      // If the scaled width is larger than target, scale to fit width instead
      if (newWidth > desiredWidth) {
        newWidth = desiredWidth;
        newHeight = Math.round(desiredWidth / inputAspectRatio);
        paddingX = 0;
        paddingY = Math.floor((desiredHeight - newHeight) / 2);
      }
    } else {
      // Input is taller than 9:16 - scale to fit width, add vertical padding
      newWidth = desiredWidth;
      newHeight = Math.round(desiredWidth / inputAspectRatio);
      paddingX = 0;
      paddingY = Math.floor((desiredHeight - newHeight) / 2);
    }

    console.log(`Scaled: ${newWidth}x${newHeight}, padding: ${paddingX},${paddingY}`);
    console.log(`Padding color: ${paddingColor}`);

    // Validate padding color for FFmpeg compatibility
    const validFFmpegColor = paddingColor === 'transparent' ? 'black@0' : paddingColor;
    console.log(`FFmpeg color: ${validFFmpegColor}`);

    ffmpeg(inputPath)
      .output(outputPath)
      .videoFilters([
        `scale=${newWidth}:${newHeight}`, // Scale to fit while maintaining aspect ratio
        `pad=${desiredWidth}:${desiredHeight}:${paddingX}:${paddingY}:${validFFmpegColor}`, // Add padding with chosen color
        'eq=brightness=0.1:saturation=1.2', // Slight enhancement for mobile viewing
      ])
      .videoCodec('libx264')
      .audioCodec('copy')
      .addOption('-avoid_negative_ts', '1')
      .addOption('-movflags', 'faststart')
      .addOption('-preset', 'fast')
      .addOption('-profile:v', 'main')
      .addOption('-crf', '23')
      .on('start', (commandLine: string) => {
        console.log('Spawned FFmpeg with command:', commandLine);
        console.log('Converting to vertical format...');
      })
      .on('end', () => {
        console.log('Vertical video conversion complete!');
        resolve(newFileName);
      })
      .on('error', (err: Error) => {
        console.error('Error converting vertical video:', err);
        reject(err);
      })
      .run();
  }
}

/**
 * Process original video format (copy without changes)
 */
async function processOriginalVideo(
  inputPath: string,
  outputPath: string,
  mediaWidth: number,
  mediaHeight: number,
  newFileName: string,
  resolve: (value: string) => void,
  reject: (reason?: any) => void
): Promise<void> {
  if (mediaWidth && mediaHeight) {
    console.log('Original video processing...');

    ffmpeg(inputPath)
      .output(outputPath)
      .videoCodec('copy') // Copy video stream without re-encoding
      .audioCodec('copy') // Copy audio stream without re-encoding
      .addOption('-avoid_negative_ts', '1') // Avoid negative timestamps
      .addOption('-movflags', 'faststart') // Optimize MP4 for streaming
      .on('start', (commandLine: string) => {
        console.log('Spawned FFmpeg with command:', commandLine);
        console.log('Copying original video...');
      })
      .on('end', () => {
        console.log('Video conversion complete!');
        resolve(newFileName);
      })
      .on('error', (err: Error) => {
        console.error('Error copying original video:', err);
        reject(err);
      })
      .run();
  } else {
    console.log('Video dimensions not detected!');
    reject(new Error('Video dimensions not detected!'));
  }
}

/**
 * Process square video format (1080x1080 - Instagram/TikTok optimized)
 * Ensures entire video is visible with white padding if needed
 */
async function processSquareVideo(
  inputPath: string,
  outputPath: string,
  mediaWidth: number,
  mediaHeight: number,
  newFileName: string,
  paddingColor: string,
  resolve: (value: string) => void,
  reject: (reason?: any) => void
): Promise<void> {
  if (mediaWidth && mediaHeight) {
    console.log('Processing square video format (1080x1080)');

    const desiredWidth = 1080;
    const desiredHeight = 1080;
    const desiredAspectRatio = desiredWidth / desiredHeight; // 1:1
    const inputAspectRatio = mediaWidth / mediaHeight;

    let newWidth: number, newHeight: number, paddingX: number, paddingY: number;

    if (inputAspectRatio > desiredAspectRatio) {
      // Input is wider than square - fit to width
      newWidth = desiredWidth;
      newHeight = Math.round(desiredWidth / inputAspectRatio);
      paddingX = 0;
      paddingY = Math.floor((desiredHeight - newHeight) / 2);
    } else {
      // Input is taller than square - fit to height
      newWidth = Math.round(desiredHeight * inputAspectRatio);
      newHeight = desiredHeight;
      paddingY = 0;
      paddingX = Math.floor((desiredWidth - newWidth) / 2);
    }

    // Validate padding color for FFmpeg compatibility
    const validFFmpegColor = paddingColor === 'transparent' ? 'white@0' : paddingColor;
    console.log(`Square padding color: ${validFFmpegColor}`);

    ffmpeg(inputPath)
      .output(outputPath)
      .videoFilters([
        `scale=${newWidth}:${newHeight}`, // Scale to fit while maintaining aspect ratio
        `pad=${desiredWidth}:${desiredHeight}:${paddingX}:${paddingY}:${validFFmpegColor}`, // Add padding with chosen color
      ])
      .audioCodec('copy')
      .addOption('-avoid_negative_ts', '1')
      .addOption('-movflags', 'faststart')
      .addOption('-preset', 'fast')
      .addOption('-profile:v', 'main')
      .addOption('-crf', '23')
      .on('start', (commandLine: string) => {
        console.log('Spawned FFmpeg with command:', commandLine);
        console.log('Converting to square format...');
      })
      .on('end', () => {
        console.log('Square video conversion complete!');
        resolve(newFileName);
      })
      .on('error', (err: Error) => {
        console.error('Error converting square video:', err);
        reject(err);
      })
      .run();
  }
}

/**
 * Process video and stream directly to response (no file saving)
 */
export function processVideoStream(
  inputPath: string,
  startTime: number,
  endTime: number,
  platform?: Platform,
  paddingColor: string = 'white',
  res?: any
): Promise<void> {
  return new Promise((resolve, reject) => {
    if (!res) {
      reject(new Error('Response object is required for streaming'));
      return;
    }

    const duration = endTime - startTime;
    const timestamp = new Date().getTime();
    const filename = `processed_video_${timestamp}.mp4`;

    // Set response headers for file download
    res.setHeader('Content-Type', 'video/mp4');
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
    res.setHeader('Transfer-Encoding', 'chunked');

    let ffmpegCommand = ffmpeg(inputPath)
      .seekInput(startTime)
      .duration(duration)
      .videoCodec('libx264')
      .audioCodec('copy')
      .addOption('-avoid_negative_ts', '1')
      .addOption('-movflags', 'frag_keyframe+empty_moov') // Enable streaming
      .format('mp4');

    // Add platform-specific processing if specified
    if (platform && platform !== 'original') {
      // Get video metadata first
      ffmpeg.ffprobe(inputPath, (err, metadata) => {
        if (err) {
          reject(err);
          return;
        }

        const videoStream = metadata.streams.find(stream => stream.codec_type === 'video');
        if (!videoStream || !videoStream.width || !videoStream.height) {
          reject(new Error('Could not determine video dimensions'));
          return;
        }

        const mediaWidth = videoStream.width;
        const mediaHeight = videoStream.height;

        // Apply platform-specific filters
        const filters = getFiltersForPlatform(platform, mediaWidth, mediaHeight, paddingColor);
        if (filters.length > 0) {
          ffmpegCommand = ffmpegCommand.videoFilters(filters);
        }

        // Stream to response
        ffmpegCommand
          .on('start', (commandLine: string) => {
            console.log('Streaming FFmpeg command:', commandLine);
          })
          .on('end', () => {
            console.log('Video streaming complete');
            resolve();
          })
          .on('error', (err: Error) => {
            console.error('Video streaming error:', err);
            if (!res.headersSent) {
              res.status(500).json({ error: 'Video processing failed' });
            }
            reject(err);
          })
          .pipe(res, { end: true });
      });
    } else {
      // No platform processing, just cut and stream
      ffmpegCommand
        .on('start', (commandLine: string) => {
          console.log('Streaming FFmpeg command:', commandLine);
        })
        .on('end', () => {
          console.log('Video streaming complete');
          resolve();
        })
        .on('error', (err: Error) => {
          console.error('Video streaming error:', err);
          if (!res.headersSent) {
            res.status(500).json({ error: 'Video processing failed' });
          }
          reject(err);
        })
        .pipe(res, { end: true });
    }
  });
}

/**
 * Get video filters for specific platform
 */
function getFiltersForPlatform(platform: Platform, mediaWidth: number, mediaHeight: number, paddingColor: string): string[] {
  const filters: string[] = [];

  if (platform === 'horizontal') {
    const desiredWidth = 1920;
    const desiredHeight = 1080;
    const { newWidth, newHeight, paddingX, paddingY } = calculateDimensions(mediaWidth, mediaHeight, desiredWidth, desiredHeight);
    const validFFmpegColor = paddingColor === 'transparent' ? 'black@0' : paddingColor;

    filters.push(`scale=${newWidth}:${newHeight}`);
    filters.push(`pad=${desiredWidth}:${desiredHeight}:${paddingX}:${paddingY}:${validFFmpegColor}`);
  } else if (platform === 'vertical') {
    const desiredWidth = 1080;
    const desiredHeight = 1920;
    const { newWidth, newHeight, paddingX, paddingY } = calculateDimensions(mediaWidth, mediaHeight, desiredWidth, desiredHeight);
    const validFFmpegColor = paddingColor === 'transparent' ? 'black@0' : paddingColor;

    filters.push(`scale=${newWidth}:${newHeight}`);
    filters.push(`pad=${desiredWidth}:${desiredHeight}:${paddingX}:${paddingY}:${validFFmpegColor}`);
    filters.push('eq=brightness=0.1:saturation=1.2');
  } else if (platform === 'square') {
    const desiredWidth = 1080;
    const desiredHeight = 1080;
    const { newWidth, newHeight, paddingX, paddingY } = calculateDimensions(mediaWidth, mediaHeight, desiredWidth, desiredHeight);
    const validFFmpegColor = paddingColor === 'transparent' ? 'white@0' : paddingColor;

    filters.push(`scale=${newWidth}:${newHeight}`);
    filters.push(`pad=${desiredWidth}:${desiredHeight}:${paddingX}:${paddingY}:${validFFmpegColor}`);
  }

  return filters;
}

/**
 * Calculate dimensions and padding for platform conversion
 */
function calculateDimensions(mediaWidth: number, mediaHeight: number, desiredWidth: number, desiredHeight: number) {
  const desiredAspectRatio = desiredWidth / desiredHeight;
  const inputAspectRatio = mediaWidth / mediaHeight;

  let newWidth: number, newHeight: number, paddingX: number, paddingY: number;

  if (inputAspectRatio > desiredAspectRatio) {
    // Input is wider - fit to width and add vertical padding
    newWidth = desiredWidth;
    newHeight = Math.round(desiredWidth / inputAspectRatio);
    paddingX = 0;
    paddingY = Math.floor((desiredHeight - newHeight) / 2);
  } else {
    // Input is taller - fit to height and add horizontal padding
    newHeight = desiredHeight;
    newWidth = Math.round(desiredHeight * inputAspectRatio);
    paddingY = 0;
    paddingX = Math.floor((desiredWidth - newWidth) / 2);
  }

  return { newWidth, newHeight, paddingX, paddingY };
}

/**
 * Convert video to MP4 format
 */
export function convertToMp4(inputPath: string, outputPath: string): Promise<string> {
  return new Promise((resolve, reject) => {
    ffmpeg(inputPath)
      .output(outputPath)
      .videoCodec('libx264') // Use H.264 codec
      .audioCodec('aac') // Use AAC codec for audio
      .outputOptions([
        '-preset fast', // Faster encoding preset
        '-crf 23', // Constant rate factor for quality control
        '-movflags +faststart' // Enables fast start for MP4
      ])
      .on('end', () => {
        console.log('Conversion finished');
        resolve(outputPath);
      })
      .on('error', (err: Error) => {
        console.error('Conversion error: ', err);
        reject(err);
      })
      .run();
  });
}
