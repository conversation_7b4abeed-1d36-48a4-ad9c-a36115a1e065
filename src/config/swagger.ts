import swaggerJsdoc from 'swagger-jsdoc';
import swaggerUi from 'swagger-ui-express';

const options = {
  definition: {
    openapi: '3.0.0',
    info: {
      title: 'Video Processing API',
      version: '2.1.0',
      description: 'A streamlined video processing API that allows users to upload, cut, convert, and download videos directly without server storage.',
      contact: {
        name: 'Video Processing API',
        email: '<EMAIL>'
      },
      license: {
        name: 'MIT',
        url: 'https://opensource.org/licenses/MIT'
      }
    },
    servers: [
      {
        url: 'http://localhost:3000',
        description: 'Development server'
      }
    ],
    tags: [
      {
        name: 'Video Processing',
        description: 'Video upload, processing, and download operations'
      },
      {
        name: 'API Info',
        description: 'API information and documentation'
      }
    ],
    components: {
      schemas: {
        VideoProcessingRequest: {
          type: 'object',
          required: ['video', 'start', 'end'],
          properties: {
            video: {
              type: 'string',
              format: 'binary',
              description: 'Video file to process (MP4, AVI, MOV, MKV, etc.)'
            },
            start: {
              type: 'number',
              minimum: 0,
              description: 'Start time in seconds',
              example: 5
            },
            end: {
              type: 'number',
              minimum: 0,
              description: 'End time in seconds (must be greater than start)',
              example: 15
            },
            platform: {
              type: 'string',
              enum: ['horizontal', 'vertical', 'square', 'original'],
              description: 'Target platform format',
              example: 'square',
              default: 'original'
            },
            paddingColor: {
              type: 'string',
              description: 'Padding color in any format (named, hex, RGB, HSL)',
              example: '#FF0000',
              default: 'white'
            }
          }
        },
        ApiInfo: {
          type: 'object',
          properties: {
            message: {
              type: 'string',
              example: 'Video Processing API - Streamlined Edition'
            },
            version: {
              type: 'string',
              example: '2.1.0'
            },
            description: {
              type: 'string',
              example: 'Upload, process, and download videos directly without server storage'
            },
            mainEndpoint: {
              type: 'string',
              example: 'POST /api/video/upload-and-cut-download'
            },
            supportedPlatforms: {
              type: 'array',
              items: {
                type: 'string'
              },
              example: ['horizontal', 'vertical', 'original', 'square']
            }
          }
        },
        ErrorResponse: {
          type: 'object',
          properties: {
            error: {
              type: 'string',
              example: 'Bad Request'
            },
            message: {
              type: 'string',
              example: 'Missing required parameters: start, end'
            }
          }
        }
      }
    }
  },
  apis: ['./src/routes/*.ts'], // Path to the API files
};

const specs = swaggerJsdoc(options);

export { swaggerUi, specs };
