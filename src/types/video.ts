export interface VideoMetadata {
  mediaWidth: number;
  mediaHeight: number;
}

export interface VideoInfo {
  format: {
    duration: number;
  };
}

export interface VideoCutParams {
  folderName: string;
  filename: string;
  start: number;
  end: number;
  outputFolder: string;
}

export interface VideoConvertParams {
  inputVideoPath: string;
  outputVideoPath: string;
  platform: 'horizontal' | 'vertical' | 'original' | 'square';
  newFileName: string;
}

export interface VideoConvertToMp4Params {
  inputPath: string;
  outputPath: string;
}

export type Platform = 'horizontal' | 'vertical' | 'original' | 'square';
